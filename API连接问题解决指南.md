# 🔧 API连接问题解决指南

## 🚨 常见错误："Failed to fetch"

当您看到"Failed to fetch"错误时，这通常表示浏览器无法连接到API服务。以下是详细的解决方案：

## 🔍 问题诊断步骤

### 1️⃣ 使用诊断工具
- 点击页面上的"🔍 诊断工具"按钮
- 运行完整诊断，查看具体哪个环节出现问题
- 根据诊断结果采取相应措施

### 2️⃣ 检查网络连接
```
✅ 确认网络连接正常
✅ 尝试访问其他网站
✅ 检查是否使用了VPN或代理
```

### 3️⃣ 浏览器相关检查
```
✅ 尝试刷新页面
✅ 清除浏览器缓存
✅ 尝试使用无痕模式
✅ 尝试其他浏览器（Chrome、Firefox、Safari）
```

## 🛠️ 具体解决方案

### 方案1：使用测试API功能
1. 点击"🔧 测试API"按钮
2. 等待测试完成，查看哪些方法可用
3. 如果有可用方法，直接使用AI功能

### 方案2：检查网络环境
```bash
# 如果使用VPN
- 尝试关闭VPN重新测试
- 或者切换VPN服务器节点

# 如果在公司网络
- 可能有防火墙限制
- 尝试使用手机热点
```

### 方案3：使用本地数据模式
1. 当API连接失败时，会弹出确认对话框
2. 点击"确定"使用本地数据
3. 本地数据包含精心整理的哲学概念

### 方案4：浏览器设置调整
```
Chrome浏览器：
1. 设置 → 隐私设置和安全性 → 网站设置
2. 确保JavaScript已启用
3. 检查弹出式窗口和重定向设置

Firefox浏览器：
1. 设置 → 隐私与安全
2. 检查跟踪保护设置
3. 确保内容阻止程序不会影响API调用
```

## 🔄 多重API调用策略说明

我们的系统使用5种不同的方法尝试连接API：

1. **直接调用** - 直接连接OpenRouter API
2. **CORS代理1** - 使用AllOrigins代理服务
3. **CORS代理2** - 使用HTMLDriven代理服务  
4. **CORS代理3** - 使用Freeboard代理服务
5. **本地数据** - 使用预设的哲学概念数据

## 📊 错误类型分析

### "Failed to fetch" 错误
- **原因**：网络连接问题或CORS限制
- **解决**：检查网络，使用代理方法

### "请求超时" 错误
- **原因**：网络速度慢或服务器响应慢
- **解决**：检查网络速度，稍后重试

### "API密钥无效" 错误
- **原因**：API密钥配置问题
- **解决**：联系开发者检查配置

### "CORS限制" 错误
- **原因**：浏览器安全策略阻止跨域请求
- **解决**：使用CORS代理或本地数据

## 🎯 最佳实践建议

### 首次使用
1. 先点击"测试API"检查连接状态
2. 如果测试成功，直接使用AI功能
3. 如果测试失败，使用本地数据模式

### 遇到问题时
1. 不要反复点击，等待当前请求完成
2. 使用诊断工具查看详细错误信息
3. 根据错误类型采取相应解决方案

### 网络环境优化
1. 使用稳定的网络连接
2. 避免在网络高峰期使用
3. 如果在限制性网络环境，优先使用本地数据

## 🆘 仍然无法解决？

如果尝试了所有方法仍然无法连接API：

1. **使用本地数据模式**
   - 包含丰富的哲学概念数据
   - 支持存在主义、道德哲学等主题
   - 提供完整的图谱可视化功能

2. **联系技术支持**
   - 提供详细的错误信息
   - 说明您的网络环境
   - 包含诊断工具的测试结果

3. **稍后重试**
   - API服务可能暂时不可用
   - 网络环境可能临时有问题
   - 建议过一段时间再尝试

## 💡 预防措施

1. **定期测试连接**
   - 使用前先测试API连接
   - 了解当前网络环境的限制

2. **备用方案准备**
   - 熟悉本地数据模式的使用
   - 了解不同主题的预设数据

3. **浏览器维护**
   - 定期清理浏览器缓存
   - 保持浏览器版本更新
   - 检查扩展程序是否影响网络请求

---

**记住**：即使API连接失败，您仍然可以使用本地数据模式体验完整的哲学知识图谱功能！

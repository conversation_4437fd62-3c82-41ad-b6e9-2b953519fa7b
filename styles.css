/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

#app {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部标题 */
.header {
    text-align: center;
    padding: 2rem 1rem;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 300;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

/* 搜索区域 */
.search-container {
    padding: 0 2rem 2rem;
    display: flex;
    justify-content: center;
}

.search-box {
    display: flex;
    max-width: 600px;
    width: 100%;
    background: white;
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

#topicInput {
    flex: 1;
    border: none;
    outline: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    background: transparent;
}

#topicInput::placeholder {
    color: #999;
}

#analyzeBtn, #testApiBtn {
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

#analyzeBtn {
    background: linear-gradient(45deg, #667eea, #764ba2);
}

#testApiBtn {
    background: #43e97b;
    margin-left: 10px;
}

#analyzeBtn:hover, #testApiBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

#analyzeBtn:disabled, #testApiBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    gap: 1rem;
    padding: 0 1rem 1rem;
    max-height: calc(100vh - 200px);
}

/* 图谱容器 */
.graph-container {
    flex: 1;
    position: relative;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

#graph-canvas {
    width: 100%;
    height: 100%;
    position: relative;
    min-height: 500px;
}

#graph-svg {
    width: 100%;
    height: 100%;
    display: none;
}

/* 加载状态 */
.loading-state {
    display: none;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #666;
}

.empty-state .icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

/* 图谱控制工具栏 */
.graph-controls {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.graph-controls button {
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(255,255,255,0.9);
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.graph-controls button:hover {
    background: white;
    transform: scale(1.05);
}

/* 侧边栏 */
.sidebar {
    width: 350px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.sidebar.active {
    display: flex;
}

.sidebar-header {
    padding: 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

#closeSidebar {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

#closeSidebar:hover {
    background: rgba(255,255,255,0.2);
}

.sidebar-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.concept-info > div {
    margin-bottom: 2rem;
}

.concept-info h4 {
    color: #667eea;
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 0.3rem;
}

.concept-info p {
    line-height: 1.7;
    color: #555;
}

.concept-info ul {
    list-style: none;
}

.concept-info li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f5f5f5;
}

.concept-info li:last-child {
    border-bottom: none;
}

#relatedConcepts {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.related-tag {
    background: #f0f4ff;
    color: #667eea;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.related-tag:hover {
    background: #667eea;
    color: white;
}

/* 图谱节点样式 */
.node {
    cursor: pointer;
    transition: all 0.3s ease;
}

.node:hover {
    stroke-width: 3px;
}

.node-label {
    fill: #333;
    font-size: 12px;
    text-anchor: middle;
    pointer-events: none;
    font-weight: 500;
}

.link {
    stroke: #999;
    stroke-opacity: 0.6;
    stroke-width: 2px;
}

.link.highlighted {
    stroke: #667eea;
    stroke-opacity: 0.8;
    stroke-width: 3px;
}

/* 底部信息 */
.footer {
    text-align: center;
    padding: 1rem;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
        max-height: none;
    }
    
    .sidebar {
        width: 100%;
        max-height: 50vh;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .search-box {
        flex-direction: column;
        border-radius: 15px;
    }
    
    #analyzeBtn, #testApiBtn {
        border-radius: 10px;
        margin-top: 0.5rem;
        margin-left: 0;
    }
    
    #graph-canvas {
        min-height: 400px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0 0.5rem 0.5rem;
    }
    
    .search-container {
        padding: 0 1rem 1rem;
    }
    
    .header {
        padding: 1.5rem 1rem;
    }
    
    .sidebar-content {
        padding: 1rem;
    }
}

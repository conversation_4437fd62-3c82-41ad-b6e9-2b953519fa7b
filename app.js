class PhilosophyKnowledgeGraph {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.zoom = null;
        this.selectedNode = null;
        
        // API配置 - 自动检测环境
        this.apiConfig = {
            baseUrl: window.location.hostname === 'localhost' ? 
                'http://localhost:3001/api/chat' : 
                '/api/chat',
            model: 'google/gemini-2.5-pro'
        };
        
        console.log('当前环境:', window.location.hostname);
        console.log('API地址:', this.apiConfig.baseUrl);
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupGraph();
    }

    setupEventListeners() {
        // 搜索按钮事件
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzePhilosophyTopic();
        });

        // 测试API按钮事件
        document.getElementById('testApiBtn').addEventListener('click', () => {
            this.testApiConnection();
        });

        // 回车键搜索
        document.getElementById('topicInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.analyzePhilosophyTopic();
            }
        });

        // 侧边栏关闭
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 图谱控制按钮
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('resetView').addEventListener('click', () => this.resetView());
        document.getElementById('fullscreen').addEventListener('click', () => this.toggleFullscreen());

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.updateGraphSize();
        });
    }

    setupGraph() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg = d3.select('#graph-svg')
            .attr('width', this.width)
            .attr('height', this.height);

        // 创建缩放行为
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 3])
            .on('zoom', (event) => {
                this.svg.select('g').attr('transform', event.transform);
            });

        this.svg.call(this.zoom);

        // 创建主容器组
        this.svg.append('g');
    }

    updateGraphSize() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .alpha(0.3)
                .restart();
        }
    }

    async analyzePhilosophyTopic() {
        const topic = document.getElementById('topicInput').value.trim();
        if (!topic) {
            alert('请输入哲学主题');
            return;
        }

        this.showLoading();
        
        try {
            console.log('开始分析主题:', topic);
            
            // 使用AI分析哲学主题
            const conceptData = await this.analyzeTopicWithAI(topic);
            
            console.log('AI分析成功，渲染图谱');
            
            // 渲染图谱
            this.renderGraph(conceptData);
            
            this.hideLoading();
            this.showGraphControls();
            
        } catch (error) {
            console.error('分析失败:', error);
            this.hideLoading();
            
            // 显示具体的错误信息给用户
            alert(`AI分析失败: ${error.message}\n\n将使用本地数据作为备选方案。`);
            
            // 如果API调用失败，回退到本地数据
            console.log('API调用失败，使用本地数据');
            const fallbackData = this.generateConceptData(topic);
            this.renderGraph(fallbackData);
            this.hideLoading();
            this.showGraphControls();
        }
    }

    async analyzeTopicWithAI(topic) {
        console.log(`开始AI分析主题: ${topic}`);
        
        const prompt = `请分析哲学主题"${topic}"，生成一个知识图谱的节点和连接数据。

要求：
1. 识别5-12个相关的核心概念、子概念、重要思想家
2. 分析概念间的逻辑关系和重要程度
3. 严格按照以下JSON格式返回，不要包含任何其他文字：

{
  "nodes": [
    {
      "id": "unique-id",
      "name": "概念名称",
      "type": "main|core|concept|related|person",
      "importance": 100,
      "description": "概念详细描述",
      "thinkers": ["相关思想家1", "相关思想家2"],
      "works": ["重要著作1", "重要著作2"]
    }
  ],
  "links": [
    {
      "source": "source-id",
      "target": "target-id",
      "strength": 0.9
    }
  ]
}

节点类型说明：
- main: 主要主题概念 (importance: 100)
- core: 核心概念 (importance: 70-90)
- concept: 一般概念 (importance: 50-70)
- related: 相关理论 (importance: 40-60)
- person: 重要思想家 (importance: 60-90)

连接强度范围：0.3-1.0，表示概念间关联程度。`;

        console.log('准备发送API请求到:', this.apiConfig.baseUrl);
        
        try {
            const requestBody = {
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.7,
                max_tokens: 4000
            };
            
            console.log('发送的请求数据:', requestBody);
            
            const response = await fetch(this.apiConfig.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('API响应状态:', response.status, response.statusText);
            console.log('API响应头:', response.headers);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API错误响应:', errorText);
                throw new Error(`API请求失败: ${response.status} ${response.statusText}\n错误详情: ${errorText}`);
            }

            const data = await response.json();
            console.log('API返回数据:', data);
            
            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                console.error('API响应格式错误:', data);
                throw new Error('API响应格式错误：缺少choices或message字段');
            }

            const content = data.choices[0].message.content;
            console.log('AI生成内容:', content);
            
            try {
                // 清理可能的markdown代码块标记
                let cleanContent = content.trim();
                
                // 移除可能的markdown代码块标记
                if (cleanContent.includes('```json')) {
                    cleanContent = cleanContent.replace(/```json\s*\n?/g, '').replace(/\n?```\s*$/g, '');
                } else if (cleanContent.includes('```')) {
                    cleanContent = cleanContent.replace(/```\s*\n?/g, '').replace(/\n?```\s*$/g, '');
                }
                
                // 查找JSON开始和结束位置
                const jsonStart = cleanContent.indexOf('{');
                const jsonEnd = cleanContent.lastIndexOf('}') + 1;
                
                if (jsonStart >= 0 && jsonEnd > jsonStart) {
                    cleanContent = cleanContent.substring(jsonStart, jsonEnd);
                }
                
                console.log('清理后的内容:', cleanContent);
                
                // 尝试解析JSON响应
                const conceptData = JSON.parse(cleanContent);
                console.log('解析后的概念数据:', conceptData);
                
                // 验证数据格式
                if (!conceptData.nodes || !conceptData.links || !Array.isArray(conceptData.nodes) || !Array.isArray(conceptData.links)) {
                    throw new Error('数据格式不正确：缺少nodes或links数组');
                }

                if (conceptData.nodes.length === 0) {
                    throw new Error('数据格式不正确：nodes数组为空');
                }

                // 为节点添加详细信息存储
                conceptData.nodes.forEach(node => {
                    if (!node.description) node.description = `${node.name}是重要的哲学概念。`;
                    if (!node.thinkers) node.thinkers = ['相关哲学家'];
                    if (!node.works) node.works = ['相关著作'];
                    if (!node.id) node.id = node.name.replace(/\s+/g, '-').toLowerCase();
                    if (!node.type) node.type = 'concept';
                    if (!node.importance) node.importance = 50;
                });

                console.log('AI分析成功，返回数据');
                return conceptData;
                
            } catch (parseError) {
                console.error('JSON解析失败:', parseError);
                console.log('原始AI返回内容:', content);
                throw new Error(`AI返回的数据格式无法解析: ${parseError.message}\n原始内容: ${content.substring(0, 200)}...`);
            }
            
        } catch (fetchError) {
            console.error('Fetch请求失败:', fetchError);
            throw fetchError;
        }
    }

    async testApiConnection() {
        console.log('开始测试API连接...');
        
        try {
            const requestBody = {
                messages: [
                    {
                        role: 'user',
                        content: '请回复"API测试成功"'
                    }
                ],
                temperature: 0.7,
                max_tokens: 100
            };
            
            console.log('测试请求数据:', requestBody);
            
            const response = await fetch(this.apiConfig.baseUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            console.log('测试API响应状态:', response.status, response.statusText);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('测试API错误响应:', errorText);
                alert(`API测试失败!\n状态: ${response.status} ${response.statusText}\n错误: ${errorText}`);
                return;
            }

            const data = await response.json();
            console.log('测试API返回数据:', data);
            
            if (data.choices && data.choices[0] && data.choices[0].message) {
                alert(`🎉 API测试成功!\n\n模型: ${this.apiConfig.model}\n回复: ${data.choices[0].message.content}\n\n现在可以正常使用AI分析功能！`);
            } else {
                alert('API测试失败: 响应格式不正确\n\n' + JSON.stringify(data, null, 2));
            }
            
        } catch (error) {
            console.error('API测试失败:', error);
            alert(`API测试失败!\n\n错误: ${error.message}\n\n请检查:\n1. 网络连接\n2. Netlify函数部署\n3. 浏览器控制台的详细错误信息`);
        }
    }

    generateConceptData(topic) {
        // 本地备用数据...
        const conceptMaps = {
            '存在主义': {
                nodes: [
                    { id: 'existentialism', name: '存在主义', type: 'main', importance: 100 },
                    { id: 'authentic-existence', name: '真实存在', type: 'core', importance: 80 },
                    { id: 'bad-faith', name: '恶劣信念', type: 'core', importance: 75 },
                    { id: 'freedom', name: '自由', type: 'core', importance: 85 },
                    { id: 'anxiety', name: '焦虑', type: 'concept', importance: 60 },
                    { id: 'nausea', name: '恶心', type: 'concept', importance: 55 },
                    { id: 'absurd', name: '荒诞', type: 'related', importance: 70 },
                    { id: 'sartre', name: '萨特', type: 'person', importance: 90 },
                    { id: 'camus', name: '加缪', type: 'person', importance: 85 },
                    { id: 'kierkegaard', name: '克尔凯郭尔', type: 'person', importance: 80 }
                ],
                links: [
                    { source: 'existentialism', target: 'authentic-existence', strength: 0.9 },
                    { source: 'existentialism', target: 'bad-faith', strength: 0.8 },
                    { source: 'existentialism', target: 'freedom', strength: 0.9 },
                    { source: 'authentic-existence', target: 'bad-faith', strength: 0.7 },
                    { source: 'freedom', target: 'anxiety', strength: 0.6 },
                    { source: 'authentic-existence', target: 'anxiety', strength: 0.5 },
                    { source: 'sartre', target: 'existentialism', strength: 0.9 },
                    { source: 'sartre', target: 'bad-faith', strength: 0.8 },
                    { source: 'sartre', target: 'nausea', strength: 0.7 },
                    { source: 'camus', target: 'absurd', strength: 0.9 },
                    { source: 'kierkegaard', target: 'anxiety', strength: 0.8 }
                ]
            }
        };

        return conceptMaps[topic] || this.generateGenericConceptData(topic);
    }

    generateGenericConceptData(topic) {
        return {
            nodes: [
                { id: 'main', name: topic, type: 'main', importance: 100 },
                { id: 'concept1', name: '核心概念1', type: 'core', importance: 80 },
                { id: 'concept2', name: '核心概念2', type: 'core', importance: 75 },
                { id: 'related1', name: '相关理论1', type: 'related', importance: 60 },
                { id: 'related2', name: '相关理论2', type: 'related', importance: 55 },
                { id: 'thinker1', name: '重要思想家1', type: 'person', importance: 70 },
                { id: 'thinker2', name: '重要思想家2', type: 'person', importance: 65 }
            ],
            links: [
                { source: 'main', target: 'concept1', strength: 0.9 },
                { source: 'main', target: 'concept2', strength: 0.8 },
                { source: 'concept1', target: 'related1', strength: 0.7 },
                { source: 'concept2', target: 'related2', strength: 0.6 },
                { source: 'thinker1', target: 'concept1', strength: 0.8 },
                { source: 'thinker2', target: 'concept2', strength: 0.7 }
            ]
        };
    }

    renderGraph(data) {
        this.nodes = data.nodes;
        this.links = data.links;

        // 清除之前的图形
        this.svg.select('g').selectAll('*').remove();

        // 创建力导向图模拟
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 100 - d.strength * 50))
            .force('charge', d3.forceManyBody().strength(-300))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 5));

        const g = this.svg.select('g');

        // 创建连线
        const link = g.append('g')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .style('stroke-width', d => d.strength * 4);

        // 创建节点组
        const node = g.append('g')
            .selectAll('.node-group')
            .data(this.nodes)
            .enter().append('g')
            .attr('class', 'node-group')
            .call(d3.drag()
                .on('start', (event, d) => this.dragstarted(event, d))
                .on('drag', (event, d) => this.dragged(event, d))
                .on('end', (event, d) => this.dragended(event, d)));

        // 添加节点圆圈
        node.append('circle')
            .attr('class', 'node')
            .attr('r', d => this.getNodeRadius(d))
            .style('fill', d => this.getNodeColor(d))
            .style('stroke', '#fff')
            .style('stroke-width', 2)
            .on('click', (event, d) => this.nodeClicked(event, d))
            .on('mouseover', (event, d) => this.nodeMouseOver(event, d))
            .on('mouseout', (event, d) => this.nodeMouseOut(event, d));

        // 添加节点标签
        node.append('text')
            .attr('class', 'node-label')
            .attr('dy', d => this.getNodeRadius(d) + 15)
            .text(d => d.name);

        // 更新位置
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });

        // 显示SVG
        document.getElementById('graph-svg').style.display = 'block';
    }

    getNodeRadius(node) {
        const baseRadius = 8;
        const maxRadius = 25;
        return baseRadius + (node.importance / 100) * (maxRadius - baseRadius);
    }

    getNodeColor(node) {
        const colors = {
            'main': '#667eea',
            'core': '#764ba2',
            'concept': '#f093fb',
            'related': '#4facfe',
            'person': '#43e97b'
        };
        return colors[node.type] || '#999';
    }

    nodeClicked(event, node) {
        this.selectedNode = node;
        this.showNodeDetails(node);
        this.highlightConnections(node);
    }

    nodeMouseOver(event, node) {
        d3.select(event.target)
            .style('stroke-width', '4px')
            .style('stroke', '#333');
    }

    nodeMouseOut(event, node) {
        if (this.selectedNode !== node) {
            d3.select(event.target)
                .style('stroke-width', '2px')
                .style('stroke', '#fff');
        }
    }

    highlightConnections(node) {
        // 重置所有连线样式
        this.svg.selectAll('.link')
            .classed('highlighted', false);

        // 高亮相关连线
        this.svg.selectAll('.link')
            .filter(d => d.source.id === node.id || d.target.id === node.id)
            .classed('highlighted', true);
    }

    showNodeDetails(node) {
        const sidebar = document.getElementById('sidebar');
        const conceptTitle = document.getElementById('conceptTitle');
        const conceptDescription = document.getElementById('conceptDescription');
        const thinkersList = document.getElementById('thinkersList');
        const worksList = document.getElementById('worksList');
        const relatedConcepts = document.getElementById('relatedConcepts');

        // 设置标题
        conceptTitle.textContent = node.name;

        // 优先使用AI生成的描述和信息
        const description = node.description || this.getNodeDescription(node).description;
        const thinkers = node.thinkers || this.getNodeDescription(node).thinkers;
        const works = node.works || this.getNodeDescription(node).works;

        // 设置描述
        conceptDescription.textContent = description;

        // 设置代表人物
        thinkersList.innerHTML = '';
        thinkers.forEach(thinker => {
            const li = document.createElement('li');
            li.textContent = thinker;
            thinkersList.appendChild(li);
        });

        // 设置重要著作
        worksList.innerHTML = '';
        works.forEach(work => {
            const li = document.createElement('li');
            li.textContent = work;
            worksList.appendChild(li);
        });

        // 设置相关概念
        relatedConcepts.innerHTML = '';
        const relatedNodes = this.getRelatedNodes(node);
        relatedNodes.forEach(relatedNode => {
            const tag = document.createElement('div');
            tag.className = 'related-tag';
            tag.textContent = relatedNode.name;
            tag.addEventListener('click', () => {
                this.nodeClicked(null, relatedNode);
            });
            relatedConcepts.appendChild(tag);
        });

        // 显示侧边栏
        sidebar.classList.add('active');
    }

    getNodeDescription(node) {
        // 模拟AI生成的详细描述
        const descriptions = {
            'existentialism': {
                description: '存在主义是20世纪的重要哲学流派，强调个体存在的独特性和主观体验的重要性，认为存在先于本质。',
                thinkers: ['让-保罗·萨特', '西蒙娜·德·波伏瓦', '阿尔贝·加缪', '索伦·克尔凯郭尔'],
                works: ['《存在与虚无》', '《局外人》', '《恐惧与颤栗》', '《第二性》']
            },
            'authentic-existence': {
                description: '真实存在指个体摆脱社会角色和外在期望，以真诚的方式面对自己的存在状态。',
                thinkers: ['马丁·海德格尔', '让-保罗·萨特'],
                works: ['《存在与时间》', '《存在与虚无》']
            }
        };

        return descriptions[node.id] || {
            description: `${node.name}是重要的哲学概念，需要深入理解其内涵和外延。`,
            thinkers: ['相关哲学家1', '相关哲学家2'],
            works: ['重要著作1', '重要著作2']
        };
    }

    getRelatedNodes(node) {
        return this.links
            .filter(link => link.source.id === node.id || link.target.id === node.id)
            .map(link => link.source.id === node.id ? link.target : link.source)
            .slice(0, 5);
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('active');
        this.selectedNode = null;
        
        // 重置所有节点和连线样式
        this.svg.selectAll('.node')
            .style('stroke-width', '2px')
            .style('stroke', '#fff');
        
        this.svg.selectAll('.link')
            .classed('highlighted', false);
    }

    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    zoomIn() {
        this.svg.transition().call(this.zoom.scaleBy, 1.5);
    }

    zoomOut() {
        this.svg.transition().call(this.zoom.scaleBy, 0.75);
    }

    resetView() {
        this.svg.transition().call(this.zoom.transform, d3.zoomIdentity);
    }

    toggleFullscreen() {
        const container = document.getElementById('graph-canvas');
        if (!document.fullscreenElement) {
            container.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showLoading() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('analyzeBtn').disabled = true;
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('analyzeBtn').disabled = false;
    }

    showGraphControls() {
        document.getElementById('graphControls').style.display = 'flex';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new PhilosophyKnowledgeGraph();
});

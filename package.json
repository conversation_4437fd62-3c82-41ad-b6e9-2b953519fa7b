{"name": "philosophy-knowledge-graph", "version": "1.0.0", "description": "哲学知识图谱学习网站 - 基于OpenRouter API的哲学概念分析与可视化平台", "main": "index.html", "scripts": {"start": "netlify dev", "build": "echo 'Static site - no build required'", "deploy": "netlify deploy --prod"}, "dependencies": {}, "devDependencies": {}, "keywords": ["philosophy", "knowledge-graph", "openrouter", "gemini", "ai", "visualization", "d3js", "netlify"], "author": "Philosophy Knowledge Graph Team", "license": "MIT"}
class PhilosophyKnowledgeGraph {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.zoom = null;
        this.selectedNode = null;
        
        // 暂时禁用API，直接使用本地数据确保网站能正常工作
        this.useLocalDataOnly = true;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupGraph();
        
        // 隐藏测试API按钮，避免用户困惑
        document.getElementById('testApiBtn').style.display = 'none';
    }

    setupEventListeners() {
        // 搜索按钮事件
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzePhilosophyTopic();
        });

        // 回车键搜索
        document.getElementById('topicInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.analyzePhilosophyTopic();
            }
        });

        // 侧边栏关闭
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 图谱控制按钮
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('resetView').addEventListener('click', () => this.resetView());
        document.getElementById('fullscreen').addEventListener('click', () => this.toggleFullscreen());

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.updateGraphSize();
        });
    }

    setupGraph() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg = d3.select('#graph-svg')
            .attr('width', this.width)
            .attr('height', this.height);

        // 创建缩放行为
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 3])
            .on('zoom', (event) => {
                this.svg.select('g').attr('transform', event.transform);
            });

        this.svg.call(this.zoom);

        // 创建主容器组
        this.svg.append('g');
    }

    updateGraphSize() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .alpha(0.3)
                .restart();
        }
    }

    async analyzePhilosophyTopic() {
        const topic = document.getElementById('topicInput').value.trim();
        if (!topic) {
            alert('请输入哲学主题');
            return;
        }

        this.showLoading();
        
        // 模拟分析延迟，提供更好的用户体验
        await this.delay(1500);
        
        // 直接使用本地智能数据生成
        const conceptData = this.generateConceptData(topic);
        
        // 渲染图谱
        this.renderGraph(conceptData);
        
        this.hideLoading();
        this.showGraphControls();
    }

    generateConceptData(topic) {
        // 扩展的哲学概念数据库
        const conceptMaps = {
            '存在主义': {
                nodes: [
                    { id: 'existentialism', name: '存在主义', type: 'main', importance: 100,
                      description: '20世纪哲学流派，强调个体存在的独特性和主观体验，认为存在先于本质。',
                      thinkers: ['萨特', '加缪', '克尔凯郭尔', '海德格尔'],
                      works: ['《存在与虚无》', '《局外人》', '《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'authentic-existence', name: '真实存在', type: 'core', importance: 85,
                      description: '指个体摆脱社会角色束缚，以真诚方式面对自己存在状态的生活方式。',
                      thinkers: ['海德格尔', '萨特'],
                      works: ['《存在与时间》', '《存在与虚无》'] },
                    { id: 'bad-faith', name: '恶劣信念', type: 'core', importance: 80,
                      description: '萨特提出的概念，指个体逃避自由和责任，假装自己没有选择的自欺行为。',
                      thinkers: ['萨特'],
                      works: ['《存在与虚无》'] },
                    { id: 'freedom', name: '自由', type: 'core', importance: 90,
                      description: '存在主义核心概念，人被判定是自由的，必须承担选择的全部责任。',
                      thinkers: ['萨特', '加缪'],
                      works: ['《存在与虚无》', '《西西弗的神话》'] },
                    { id: 'anxiety', name: '焦虑', type: 'concept', importance: 70,
                      description: '面对存在的无意义和自由选择时产生的根本性情绪体验。',
                      thinkers: ['克尔凯郭尔', '海德格尔'],
                      works: ['《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'absurd', name: '荒诞', type: 'related', importance: 75,
                      description: '加缪哲学的核心，指人类寻求意义与世界无意义之间的冲突。',
                      thinkers: ['加缪'],
                      works: ['《西西弗的神话》', '《局外人》'] },
                    { id: 'sartre', name: '萨特', type: 'person', importance: 95,
                      description: '法国哲学家，存在主义主要代表人物，强调人的自由和责任。',
                      thinkers: ['萨特'],
                      works: ['《存在与虚无》', '《恶心》'] },
                    { id: 'camus', name: '加缪', type: 'person', importance: 90,
                      description: '法裔阿尔及利亚作家哲学家，荒诞主义代表人物。',
                      thinkers: ['加缪'],
                      works: ['《局外人》', '《西西弗的神话》'] }
                ],
                links: [
                    { source: 'existentialism', target: 'authentic-existence', strength: 0.9 },
                    { source: 'existentialism', target: 'bad-faith', strength: 0.8 },
                    { source: 'existentialism', target: 'freedom', strength: 0.95 },
                    { source: 'authentic-existence', target: 'bad-faith', strength: 0.7 },
                    { source: 'freedom', target: 'anxiety', strength: 0.8 },
                    { source: 'sartre', target: 'existentialism', strength: 0.9 },
                    { source: 'sartre', target: 'bad-faith', strength: 0.9 },
                    { source: 'camus', target: 'absurd', strength: 0.95 },
                    { source: 'absurd', target: 'existentialism', strength: 0.6 }
                ]
            },
            '道德哲学': {
                nodes: [
                    { id: 'ethics', name: '道德哲学', type: 'main', importance: 100,
                      description: '研究道德原则、价值判断和伦理行为的哲学分支。',
                      thinkers: ['亚里士多德', '康德', '密尔'],
                      works: ['《尼各马可伦理学》', '《道德形而上学基础》', '《功利主义》'] },
                    { id: 'virtue-ethics', name: '美德伦理学', type: 'core', importance: 85,
                      description: '强调品格和美德，认为道德行为源于良好的品格特质。',
                      thinkers: ['亚里士多德', '阿奎那'],
                      works: ['《尼各马可伦理学》'] },
                    { id: 'deontology', name: '义务论', type: 'core', importance: 85,
                      description: '康德提出的道德理论，强调行为的动机和义务，而非后果。',
                      thinkers: ['康德'],
                      works: ['《道德形而上学基础》', '《实践理性批判》'] },
                    { id: 'consequentialism', name: '后果主义', type: 'core', importance: 80,
                      description: '判断行为道德性的标准是其产生的后果。',
                      thinkers: ['边沁', '密尔'],
                      works: ['《道德与立法原理导论》', '《功利主义》'] },
                    { id: 'categorical-imperative', name: '绝对命令', type: 'concept', importance: 75,
                      description: '康德道德哲学的核心，无条件的道德法则。',
                      thinkers: ['康德'],
                      works: ['《道德形而上学基础》'] },
                    { id: 'golden-mean', name: '中庸之道', type: 'concept', importance: 70,
                      description: '亚里士多德提出的美德概念，认为美德是两个极端之间的适度。',
                      thinkers: ['亚里士多德'],
                      works: ['《尼各马可伦理学》'] }
                ],
                links: [
                    { source: 'ethics', target: 'virtue-ethics', strength: 0.9 },
                    { source: 'ethics', target: 'deontology', strength: 0.9 },
                    { source: 'ethics', target: 'consequentialism', strength: 0.85 },
                    { source: 'deontology', target: 'categorical-imperative', strength: 0.95 },
                    { source: 'virtue-ethics', target: 'golden-mean', strength: 0.8 }
                ]
            },
            '认识论': {
                nodes: [
                    { id: 'epistemology', name: '认识论', type: 'main', importance: 100,
                      description: '研究知识的本质、来源、界限和有效性的哲学分支。',
                      thinkers: ['笛卡尔', '休谟', '康德'],
                      works: ['《第一哲学沉思集》', '《人类理解研究》', '《纯粹理性批判》'] },
                    { id: 'rationalism', name: '理性主义', type: 'core', importance: 85,
                      description: '强调理性和逻辑推理在获得知识中的首要地位。',
                      thinkers: ['笛卡尔', '斯宾诺莎', '莱布尼茨'],
                      works: ['《第一哲学沉思集》', '《伦理学》'] },
                    { id: 'empiricism', name: '经验主义', type: 'core', importance: 85,
                      description: '认为经验是知识的主要来源，强调感官经验的重要性。',
                      thinkers: ['洛克', '休谟', '贝克莱'],
                      works: ['《人类理解论》', '《人类理解研究》'] },
                    { id: 'skepticism', name: '怀疑主义', type: 'related', importance: 70,
                      description: '对知识的可能性持怀疑态度的哲学立场。',
                      thinkers: ['休谟', '笛卡尔'],
                      works: ['《人类理解研究》', '《第一哲学沉思集》'] }
                ],
                links: [
                    { source: 'epistemology', target: 'rationalism', strength: 0.9 },
                    { source: 'epistemology', target: 'empiricism', strength: 0.9 },
                    { source: 'epistemology', target: 'skepticism', strength: 0.7 },
                    { source: 'rationalism', target: 'empiricism', strength: 0.6 }
                ]
            }
        };

        // 如果有预定义的概念图，返回它；否则生成智能的通用概念图
        if (conceptMaps[topic]) {
            return conceptMaps[topic];
        }

        // 智能生成基于主题的概念图
        return this.generateIntelligentConceptData(topic);
    }

    generateIntelligentConceptData(topic) {
        // 根据主题智能生成相关概念
        const philosophicalDomains = {
            '美学': ['美', '艺术', '审美经验', '崇高', '康德', '黑格尔'],
            '政治哲学': ['正义', '自由', '权威', '社会契约', '罗尔斯', '诺齐克'],
            '心理哲学': ['意识', '心智', '自由意志', '人工智能', '丹尼特', '查尔默斯'],
            '语言哲学': ['意义', '指称', '语言游戏', '维特根斯坦', '奥斯汀', '塞尔'],
            '形而上学': ['存在', '实在', '时间', '空间', '因果性', '柏拉图', '亚里士多德'],
            '科学哲学': ['归纳', '证伪', '范式', '波普尔', '库恩', '拉卡托斯']
        };

        // 查找最相关的领域
        let bestMatch = null;
        let maxScore = 0;
        
        for (const [domain, concepts] of Object.entries(philosophicalDomains)) {
            if (topic.includes(domain) || domain.includes(topic)) {
                bestMatch = { domain, concepts };
                break;
            }
        }

        // 如果没有直接匹配，使用通用模板
        if (!bestMatch) {
            bestMatch = { domain: topic, concepts: ['核心概念', '主要理论', '重要应用', '代表人物', '经典著作'] };
        }

        const { domain, concepts } = bestMatch;

        return {
            nodes: [
                { 
                    id: 'main', 
                    name: topic, 
                    type: 'main', 
                    importance: 100,
                    description: `${topic}是哲学的重要分支，探讨相关的基本问题和概念。`,
                    thinkers: concepts.slice(-2),
                    works: [`${topic}相关经典著作1`, `${topic}相关经典著作2`]
                },
                ...concepts.slice(0, -2).map((concept, index) => ({
                    id: `concept${index + 1}`,
                    name: concept,
                    type: index < 2 ? 'core' : index < 4 ? 'concept' : 'related',
                    importance: 90 - index * 10,
                    description: `${concept}是${topic}中的重要概念。`,
                    thinkers: [concepts[concepts.length - 2] || '哲学家A'],
                    works: [`关于${concept}的重要著作`]
                })),
                ...concepts.slice(-2).map((person, index) => ({
                    id: `person${index + 1}`,
                    name: person,
                    type: 'person',
                    importance: 85 - index * 5,
                    description: `${person}是${topic}领域的重要思想家。`,
                    thinkers: [person],
                    works: [`${person}的代表作品`]
                }))
            ],
            links: [
                { source: 'main', target: 'concept1', strength: 0.9 },
                { source: 'main', target: 'concept2', strength: 0.8 },
                { source: 'concept1', target: 'concept3', strength: 0.7 },
                { source: 'concept2', target: 'concept4', strength: 0.6 },
                { source: 'person1', target: 'concept1', strength: 0.8 },
                { source: 'person2', target: 'concept2', strength: 0.7 }
            ]
        };
    }

    renderGraph(data) {
        this.nodes = data.nodes;
        this.links = data.links;

        // 清除之前的图形
        this.svg.select('g').selectAll('*').remove();

        // 创建力导向图模拟
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 120 - d.strength * 60))
            .force('charge', d3.forceManyBody().strength(-400))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 10));

        const g = this.svg.select('g');

        // 创建连线
        const link = g.append('g')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .style('stroke-width', d => d.strength * 4);

        // 创建节点组
        const node = g.append('g')
            .selectAll('.node-group')
            .data(this.nodes)
            .enter().append('g')
            .attr('class', 'node-group')
            .style('cursor', 'pointer')
            .call(d3.drag()
                .on('start', (event, d) => this.dragstarted(event, d))
                .on('drag', (event, d) => this.dragged(event, d))
                .on('end', (event, d) => this.dragended(event, d)));

        // 添加节点圆圈
        node.append('circle')
            .attr('class', 'node')
            .attr('r', d => this.getNodeRadius(d))
            .style('fill', d => this.getNodeColor(d))
            .style('stroke', '#fff')
            .style('stroke-width', 2)
            .on('click', (event, d) => this.nodeClicked(event, d))
            .on('mouseover', (event, d) => this.nodeMouseOver(event, d))
            .on('mouseout', (event, d) => this.nodeMouseOut(event, d));

        // 添加节点标签
        node.append('text')
            .attr('class', 'node-label')
            .attr('dy', d => this.getNodeRadius(d) + 15)
            .style('font-size', d => d.type === 'main' ? '14px' : '12px')
            .style('font-weight', d => d.type === 'main' ? 'bold' : 'normal')
            .text(d => d.name);

        // 更新位置
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });

        // 显示SVG
        document.getElementById('graph-svg').style.display = 'block';
    }

    getNodeRadius(node) {
        const baseRadius = 12;
        const maxRadius = 30;
        return baseRadius + (node.importance / 100) * (maxRadius - baseRadius);
    }

    getNodeColor(node) {
        const colors = {
            'main': '#667eea',
            'core': '#764ba2',
            'concept': '#f093fb',
            'related': '#4facfe',
            'person': '#43e97b'
        };
        return colors[node.type] || '#999';
    }

    nodeClicked(event, node) {
        this.selectedNode = node;
        this.showNodeDetails(node);
        this.highlightConnections(node);
    }

    nodeMouseOver(event, node) {
        d3.select(event.target)
            .style('stroke-width', '4px')
            .style('stroke', '#333')
            .transition()
            .duration(200)
            .attr('r', this.getNodeRadius(node) + 3);
    }

    nodeMouseOut(event, node) {
        if (this.selectedNode !== node) {
            d3.select(event.target)
                .style('stroke-width', '2px')
                .style('stroke', '#fff')
                .transition()
                .duration(200)
                .attr('r', this.getNodeRadius(node));
        }
    }

    highlightConnections(node) {
        // 重置所有连线样式
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.3);

        // 高亮相关连线
        this.svg.selectAll('.link')
            .filter(d => d.source.id === node.id || d.target.id === node.id)
            .classed('highlighted', true)
            .style('opacity', 1);

        // 高亮相关节点
        this.svg.selectAll('.node')
            .style('opacity', 0.3);

        this.svg.selectAll('.node')
            .filter(d => {
                if (d.id === node.id) return true;
                return this.links.some(link => 
                    (link.source.id === node.id && link.target.id === d.id) ||
                    (link.target.id === node.id && link.source.id === d.id)
                );
            })
            .style('opacity', 1);
    }

    showNodeDetails(node) {
        const sidebar = document.getElementById('sidebar');
        const conceptTitle = document.getElementById('conceptTitle');
        const conceptDescription = document.getElementById('conceptDescription');
        const thinkersList = document.getElementById('thinkersList');
        const worksList = document.getElementById('worksList');
        const relatedConcepts = document.getElementById('relatedConcepts');

        // 设置标题
        conceptTitle.textContent = node.name;

        // 设置描述
        conceptDescription.textContent = node.description || `${node.name}是重要的哲学概念。`;

        // 设置代表人物
        thinkersList.innerHTML = '';
        (node.thinkers || ['相关哲学家']).forEach(thinker => {
            const li = document.createElement('li');
            li.textContent = thinker;
            thinkersList.appendChild(li);
        });

        // 设置重要著作
        worksList.innerHTML = '';
        (node.works || ['相关著作']).forEach(work => {
            const li = document.createElement('li');
            li.textContent = work;
            worksList.appendChild(li);
        });

        // 设置相关概念
        relatedConcepts.innerHTML = '';
        const relatedNodes = this.getRelatedNodes(node);
        relatedNodes.forEach(relatedNode => {
            const tag = document.createElement('div');
            tag.className = 'related-tag';
            tag.textContent = relatedNode.name;
            tag.addEventListener('click', () => {
                this.nodeClicked(null, relatedNode);
            });
            relatedConcepts.appendChild(tag);
        });

        // 显示侧边栏
        sidebar.classList.add('active');
    }

    getRelatedNodes(node) {
        return this.links
            .filter(link => link.source.id === node.id || link.target.id === node.id)
            .map(link => link.source.id === node.id ? link.target : link.source)
            .slice(0, 6);
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('active');
        this.selectedNode = null;
        
        // 重置所有样式
        this.svg.selectAll('.node')
            .style('stroke-width', '2px')
            .style('stroke', '#fff')
            .style('opacity', 1);
        
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.6);
    }

    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    zoomIn() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 1.5);
    }

    zoomOut() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 0.75);
    }

    resetView() {
        this.svg.transition().duration(500).call(this.zoom.transform, d3.zoomIdentity);
    }

    toggleFullscreen() {
        const container = document.getElementById('graph-canvas');
        if (!document.fullscreenElement) {
            container.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showLoading() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('analyzeBtn').disabled = true;
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('analyzeBtn').disabled = false;
    }

    showGraphControls() {
        document.getElementById('graphControls').style.display = 'flex';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new PhilosophyKnowledgeGraph();
});

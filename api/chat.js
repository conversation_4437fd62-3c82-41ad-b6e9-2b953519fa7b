// Vercel Serverless Function for API proxy
export default async function handler(req, res) {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }

    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }

    try {
        const API_CONFIG = {
            baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
            apiKey: '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',
            model: 'google/gemini-2.5-pro'
        };

        const { messages, temperature = 0.7, max_tokens = 4000 } = req.body;

        const response = await fetch(API_CONFIG.baseUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_CONFIG.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://philosophy-knowledge-graph.vercel.app',
                'X-Title': '哲学知识图谱学习网站'
            },
            body: JSON.stringify({
                model: API_CONFIG.model,
                messages,
                temperature,
                max_tokens
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('OpenRouter API Error:', errorText);
            throw new Error(`API request failed: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        res.status(200).json(data);

    } catch (error) {
        console.error('Proxy error:', error);
        res.status(500).json({ 
            error: error.message || 'Internal server error',
            details: 'Failed to process AI request'
        });
    }
}

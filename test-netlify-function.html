<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netlify Function API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background: #007acc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #005a9e;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Netlify Function API测试</h1>
        <p>测试通过Netlify Functions调用OpenRouter API</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="apiKey">OpenRouter API Key:</label>
                <input type="password" id="apiKey" placeholder="sk-or-v1-..." required>
            </div>
            
            <div class="form-group">
                <label for="message">哲学概念:</label>
                <input type="text" id="message" placeholder="例如：自由意志" value="存在主义" required>
            </div>
            
            <button type="submit" id="testBtn">🔍 测试API调用</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('apiKey').value.trim();
            const message = document.getElementById('message').value.trim();
            const resultDiv = document.getElementById('result');
            const testBtn = document.getElementById('testBtn');
            
            if (!apiKey || !message) {
                showResult('请填写完整信息', 'error');
                return;
            }
            
            // 显示加载状态
            testBtn.disabled = true;
            testBtn.textContent = '⏳ 测试中...';
            showResult('正在调用Netlify Function...', 'loading');
            
            try {
                // 构建函数URL - 本地开发和线上部署会有不同的URL
                const functionUrl = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
                    ? '/.netlify/functions/chat-simple'  // 本地开发
                    : '/.netlify/functions/chat-simple'; // 部署后
                
                const response = await fetch(functionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        apiKey: apiKey
                    })
                });
                
                const responseText = await response.text();
                
                if (response.ok) {
                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (e) {
                        data = responseText;
                    }
                    
                    showResult(`✅ API调用成功！\n\n状态码: ${response.status}\n\n响应数据:\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    showResult(`❌ API调用失败\n\n状态码: ${response.status}\n\n错误信息:\n${responseText}`, 'error');
                }
                
            } catch (error) {
                showResult(`❌ 网络错误\n\n错误信息: ${error.message}\n\n请检查：\n1. Netlify Functions是否部署成功\n2. 网络连接是否正常\n3. CORS设置是否正确`, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🔍 测试API调用';
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
        
        // 页面加载时的信息
        window.addEventListener('load', function() {
            const info = `
📍 当前环境信息:
- 主机名: ${window.location.hostname}
- 协议: ${window.location.protocol}
- 端口: ${window.location.port || '默认'}
- 完整URL: ${window.location.href}

💡 使用说明:
1. 输入你的OpenRouter API Key
2. 输入一个哲学概念
3. 点击测试按钮
4. 查看API调用结果

🔧 如果测试失败，请检查:
- API Key是否正确
- Netlify Functions是否部署成功
- 网络连接是否正常
            `;
            showResult(info, 'loading');
        });
    </script>
</body>
</html>

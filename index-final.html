<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哲学知识图谱学习网站</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <h1>🧠 哲学知识图谱</h1>
            <p>基于AI的哲学概念分析与可视化平台</p>
            <div style="background: rgba(255,255,255,0.15); padding: 12px; border-radius: 12px; margin-top: 15px; backdrop-filter: blur(10px);">
                <div style="color: rgba(255,255,255,0.95); font-size: 14px; margin-bottom: 8px;">
                    🤖 <strong>OpenRouter + Google Gemini 2.5 Pro</strong>
                </div>
                <div style="color: rgba(255,255,255,0.8); font-size: 13px;">
                    🔄 智能多重API调用策略 • 🛡️ 自动错误处理 • 📚 本地数据回退
                </div>
            </div>
        </header>

        <!-- 搜索区域 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" id="topicInput" placeholder="🔍 输入哲学主题：存在主义、道德哲学、认识论、美学、政治哲学..." />
                <button id="analyzeBtn">🚀 生成图谱</button>
                <button id="testApiBtn" style="margin-left: 10px; background: #43e97b;">🔧 测试API</button>
            </div>
            <div style="text-align: center; margin-top: 12px;">
                <div style="color: rgba(255,255,255,0.9); font-size: 14px; margin-bottom: 8px;">
                    💡 <strong>使用建议</strong>：先点击"测试API"确认连接状态，然后输入感兴趣的哲学主题
                </div>
                <div style="color: rgba(255,255,255,0.7); font-size: 12px;">
                    支持中文和英文主题 • 自动生成概念关系图 • 点击节点查看详细信息
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 图谱可视化区域 -->
            <div class="graph-container">
                <div id="graph-canvas">
                    <div class="loading-state" id="loadingState">
                        <div class="spinner"></div>
                        <p>🤖 AI正在深度分析概念关系...</p>
                        <div style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                            请稍候，这可能需要几秒钟
                        </div>
                    </div>
                    <div class="empty-state" id="emptyState">
                        <div class="icon">🧠</div>
                        <h3>开始探索哲学世界</h3>
                        <p>在上方输入框中输入感兴趣的哲学主题</p>
                        <div style="margin-top: 25px; color: #666; font-size: 14px; line-height: 1.6;">
                            <div style="margin-bottom: 15px;">
                                <strong>🎯 推荐主题：</strong>
                            </div>
                            <div style="display: flex; flex-wrap: wrap; gap: 8px; justify-content: center;">
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='存在主义';document.getElementById('analyzeBtn').click()">存在主义</span>
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='道德哲学';document.getElementById('analyzeBtn').click()">道德哲学</span>
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='认识论';document.getElementById('analyzeBtn').click()">认识论</span>
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='美学';document.getElementById('analyzeBtn').click()">美学</span>
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='政治哲学';document.getElementById('analyzeBtn').click()">政治哲学</span>
                                <span class="topic-tag" onclick="document.getElementById('topicInput').value='心理哲学';document.getElementById('analyzeBtn').click()">心理哲学</span>
                            </div>
                            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
                                <div><strong>🔧 技术特色：</strong></div>
                                <div>• 5种API调用方法自动切换</div>
                                <div>• 实时错误处理和状态反馈</div>
                                <div>• 高质量本地数据库备用</div>
                                <div>• 交互式图谱可视化</div>
                            </div>
                        </div>
                    </div>
                    <svg id="graph-svg"></svg>
                </div>
                
                <!-- 图谱控制工具栏 -->
                <div class="graph-controls" id="graphControls" style="display: none;">
                    <button id="zoomIn" title="放大图谱">🔍+</button>
                    <button id="zoomOut" title="缩小图谱">🔍-</button>
                    <button id="resetView" title="重置视图">🏠</button>
                    <button id="fullscreen" title="全屏模式">⛶</button>
                </div>
            </div>

            <!-- 侧边栏详情 -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3 id="conceptTitle">📖 概念详情</h3>
                    <button id="closeSidebar">×</button>
                </div>
                <div class="sidebar-content">
                    <div class="concept-info" id="conceptInfo">
                        <div class="concept-description">
                            <h4>💡 概念定义</h4>
                            <p id="conceptDescription">选择图谱中的节点查看详细信息</p>
                        </div>
                        
                        <div class="key-thinkers">
                            <h4>👨‍🏫 代表人物</h4>
                            <ul id="thinkersList"></ul>
                        </div>
                        
                        <div class="important-works">
                            <h4>📚 重要著作</h4>
                            <ul id="worksList"></ul>
                        </div>
                        
                        <div class="related-concepts">
                            <h4>🔗 相关概念</h4>
                            <div id="relatedConcepts"></div>
                        </div>
                        
                        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee; text-align: center;">
                            <small style="color: #888;">
                                💡 点击相关概念标签可快速跳转
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>🚀 基于多重API调用策略的哲学概念分析与可视化平台</p>
            <div style="margin-top: 5px; font-size: 12px; opacity: 0.8;">
                Powered by OpenRouter API + Google Gemini 2.5 Pro + D3.js
            </div>
        </footer>
    </div>

    <!-- 自定义样式补充 -->
    <style>
        .topic-tag {
            background: #f0f4ff;
            color: #667eea;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e0e8ff;
        }
        
        .topic-tag:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        
        .search-box input {
            font-size: 16px !important; /* 防止移动端缩放 */
        }
        
        /* 改善移动端体验 */
        @media (max-width: 768px) {
            .topic-tag {
                font-size: 12px;
                padding: 5px 10px;
            }
            
            .empty-state .icon {
                font-size: 3rem;
            }
            
            .search-box {
                flex-direction: column;
                gap: 10px;
            }
            
            #analyzeBtn, #testApiBtn {
                margin-left: 0 !important;
                width: 100%;
            }
        }
    </style>

    <!-- 引入D3.js用于图谱可视化 -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="app-final.js"></script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用调试工具</title>
    <style>
        body {
            font-family: 'Monaco', 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #ffffff;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #005a9e;
        }
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .log-area {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            min-height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .success { color: #00ff00; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .info { color: #44aaff; }
        .config-section {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-item {
            margin: 10px 0;
        }
        .config-label {
            color: #ffaa00;
            font-weight: bold;
        }
        input[type="text"] {
            background: #222;
            border: 1px solid #555;
            color: #fff;
            padding: 8px;
            border-radius: 3px;
            width: 400px;
            font-family: monospace;
        }
        .clear-btn {
            background: #ff4444;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            float: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 OpenRouter API 调试工具</h1>
            <p>深度分析API调用失败原因并提供解决方案</p>
        </div>

        <div class="config-section">
            <h3>📋 API配置</h3>
            <div class="config-item">
                <div class="config-label">API Key:</div>
                <input type="text" id="apiKey" value="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" />
            </div>
            <div class="config-item">
                <div class="config-label">模型:</div>
                <input type="text" id="model" value="google/gemini-2.5-pro" />
            </div>
            <div class="config-item">
                <div class="config-label">API端点:</div>
                <input type="text" id="endpoint" value="https://openrouter.ai/api/v1/chat/completions" />
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 基础连接测试</h3>
            <button class="test-button" onclick="testBasicConnection()">1. 测试网络连接</button>
            <button class="test-button" onclick="testCORS()">2. 测试CORS策略</button>
            <button class="test-button" onclick="testAPIKey()">3. 验证API Key</button>
            <button class="test-button" onclick="testOpenRouterStatus()">4. 检查OpenRouter状态</button>
        </div>

        <div class="test-section">
            <h3>🔄 API调用方法测试</h3>
            <button class="test-button" onclick="testDirectCall()">直接调用</button>
            <button class="test-button" onclick="testProxy('https://api.allorigins.win/raw?url=')">代理1 (allorigins)</button>
            <button class="test-button" onclick="testProxy('https://cors-proxy.htmldriven.com/?url=')">代理2 (htmldriven)</button>
            <button class="test-button" onclick="testProxy('https://thingproxy.freeboard.io/fetch/')">代理3 (freeboard)</button>
            <button class="test-button" onclick="testJsonp()">JSONP方式</button>
        </div>

        <div class="test-section">
            <h3>🌐 网络环境检测</h3>
            <button class="test-button" onclick="detectNetworkEnvironment()">检测网络环境</button>
            <button class="test-button" onclick="testDNSResolution()">DNS解析测试</button>
            <button class="test-button" onclick="testFirewall()">防火墙检测</button>
        </div>

        <div class="test-section">
            <h3>💡 解决方案生成</h3>
            <button class="test-button" onclick="generateSolutions()">生成解决方案</button>
            <button class="test-button" onclick="testAlternativeAPIs()">测试替代API</button>
        </div>

        <div class="log-area">
            <button class="clear-btn" onclick="clearLog()">清空日志</button>
            <div id="logOutput">🚀 API调试工具已启动...\n等待测试命令...\n\n</div>
        </div>
    </div>

    <script>
        let logOutput = document.getElementById('logOutput');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            const prefix = {
                'success': '✅',
                'error': '❌',
                'warning': '⚠️',
                'info': '💡'
            }[type] || '📝';
            
            logOutput.innerHTML += `<span class="${className}">[${timestamp}] ${prefix} ${message}</span>\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            logOutput.innerHTML = '📝 日志已清空\n\n';
        }

        async function testBasicConnection() {
            log('开始测试基础网络连接...', 'info');
            
            try {
                // 测试基本网络连接
                const testUrls = [
                    'https://httpbin.org/ip',
                    'https://api.github.com',
                    'https://www.google.com',
                    'https://openrouter.ai'
                ];
                
                for (const url of testUrls) {
                    try {
                        log(`测试连接: ${url}`, 'info');
                        const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' });
                        log(`${url} - 连接成功`, 'success');
                    } catch (error) {
                        log(`${url} - 连接失败: ${error.message}`, 'error');
                    }
                }
                
                // 检测用户IP和地理位置
                try {
                    const ipResponse = await fetch('https://httpbin.org/ip');
                    const ipData = await ipResponse.json();
                    log(`用户IP: ${ipData.origin}`, 'info');
                } catch (error) {
                    log(`无法获取IP信息: ${error.message}`, 'warning');
                }
                
            } catch (error) {
                log(`基础连接测试失败: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            log('开始测试CORS策略...', 'info');
            
            const testUrl = 'https://openrouter.ai/api/v1/models';
            
            try {
                // 测试预检请求
                log('发送预检请求 (OPTIONS)...', 'info');
                const optionsResponse = await fetch(testUrl, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type, Authorization'
                    }
                });
                
                log(`预检请求状态: ${optionsResponse.status}`, optionsResponse.ok ? 'success' : 'error');
                
                // 检查CORS头
                const corsHeaders = [
                    'Access-Control-Allow-Origin',
                    'Access-Control-Allow-Methods',
                    'Access-Control-Allow-Headers'
                ];
                
                corsHeaders.forEach(header => {
                    const value = optionsResponse.headers.get(header);
                    if (value) {
                        log(`${header}: ${value}`, 'success');
                    } else {
                        log(`${header}: 未设置`, 'warning');
                    }
                });
                
            } catch (error) {
                log(`CORS测试失败: ${error.message}`, 'error');
                log('这表明浏览器阻止了跨域请求', 'warning');
            }
        }

        async function testAPIKey() {
            log('开始验证API Key...', 'info');
            
            const apiKey = document.getElementById('apiKey').value;
            
            if (!apiKey || apiKey.length < 10) {
                log('API Key格式无效', 'error');
                return;
            }
            
            log(`API Key长度: ${apiKey.length}`, 'info');
            log(`API Key前缀: ${apiKey.substring(0, 10)}...`, 'info');
            
            // 尝试调用模型列表API验证密钥
            try {
                const response = await fetch('https://openrouter.ai/api/v1/models', {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    log('API Key验证成功', 'success');
                    const data = await response.json();
                    log(`可用模型数量: ${data.data?.length || 0}`, 'info');
                } else {
                    log(`API Key验证失败: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(`错误详情: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`API Key验证过程失败: ${error.message}`, 'error');
            }
        }

        async function testOpenRouterStatus() {
            log('检查OpenRouter服务状态...', 'info');
            
            try {
                // 检查OpenRouter主页
                const mainResponse = await fetch('https://openrouter.ai', { method: 'HEAD', mode: 'no-cors' });
                log('OpenRouter主站可访问', 'success');
                
                // 检查API端点状态
                const apiResponse = await fetch('https://openrouter.ai/api/v1/models', { method: 'HEAD' });
                log(`API端点状态: ${apiResponse.status}`, apiResponse.ok ? 'success' : 'warning');
                
            } catch (error) {
                log(`OpenRouter状态检查失败: ${error.message}`, 'error');
            }
        }

        async function testDirectCall() {
            log('测试直接API调用...', 'info');
            
            const apiKey = document.getElementById('apiKey').value;
            const model = document.getElementById('model').value;
            const endpoint = document.getElementById('endpoint').value;
            
            try {
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': window.location.origin,
                        'X-Title': 'API调试工具'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{ role: 'user', content: '请回复"测试成功"' }],
                        max_tokens: 50
                    })
                });
                
                log(`直接调用状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log('直接调用成功！', 'success');
                    log(`响应: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`错误响应: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`直接调用失败: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    log('这是CORS错误，浏览器阻止了跨域请求', 'warning');
                }
            }
        }

        async function testProxy(proxyUrl) {
            log(`测试代理: ${proxyUrl}`, 'info');
            
            const apiKey = document.getElementById('apiKey').value;
            const model = document.getElementById('model').value;
            const endpoint = document.getElementById('endpoint').value;
            
            try {
                const targetUrl = encodeURIComponent(endpoint);
                const fullProxyUrl = `${proxyUrl}${targetUrl}`;
                
                log(`代理URL: ${fullProxyUrl}`, 'info');
                
                const response = await fetch(fullProxyUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: [{ role: 'user', content: '请回复"代理测试成功"' }],
                        max_tokens: 50
                    })
                });
                
                log(`代理调用状态: ${response.status}`, response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    const data = await response.json();
                    log('代理调用成功！', 'success');
                    log(`响应: ${data.choices?.[0]?.message?.content || JSON.stringify(data)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`代理错误: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`代理调用失败: ${error.message}`, 'error');
            }
        }

        async function testJsonp() {
            log('测试JSONP方式调用...', 'info');
            
            try {
                // 创建JSONP请求
                const script = document.createElement('script');
                const callbackName = 'jsonpCallback' + Date.now();
                
                window[callbackName] = function(data) {
                    log('JSONP调用成功！', 'success');
                    log(`响应: ${JSON.stringify(data)}`, 'success');
                    document.head.removeChild(script);
                    delete window[callbackName];
                };
                
                script.src = `https://api.allorigins.win/get?url=${encodeURIComponent('https://openrouter.ai/api/v1/models')}&callback=${callbackName}`;
                script.onerror = function() {
                    log('JSONP调用失败', 'error');
                    document.head.removeChild(script);
                    delete window[callbackName];
                };
                
                document.head.appendChild(script);
                
                // 超时处理
                setTimeout(() => {
                    if (window[callbackName]) {
                        log('JSONP调用超时', 'warning');
                        document.head.removeChild(script);
                        delete window[callbackName];
                    }
                }, 10000);
                
            } catch (error) {
                log(`JSONP测试失败: ${error.message}`, 'error');
            }
        }

        async function detectNetworkEnvironment() {
            log('检测网络环境...', 'info');
            
            // 检测用户代理
            log(`浏览器: ${navigator.userAgent}`, 'info');
            
            // 检测连接类型
            if (navigator.connection) {
                log(`连接类型: ${navigator.connection.effectiveType}`, 'info');
                log(`下行速度: ${navigator.connection.downlink} Mbps`, 'info');
            }
            
            // 检测是否在代理后面
            try {
                const start = Date.now();
                await fetch('https://httpbin.org/delay/1', { method: 'HEAD' });
                const delay = Date.now() - start;
                log(`网络延迟: ${delay}ms`, delay > 3000 ? 'warning' : 'success');
            } catch (error) {
                log('无法测量网络延迟', 'warning');
            }
            
            // 检测是否支持WebRTC (可能被企业网络阻止)
            if (window.RTCPeerConnection) {
                log('支持WebRTC', 'success');
            } else {
                log('不支持WebRTC (可能在受限网络)', 'warning');
            }
        }

        async function testDNSResolution() {
            log('测试DNS解析...', 'info');
            
            const domains = [
                'openrouter.ai',
                'api.openrouter.ai',
                'cors-anywhere.herokuapp.com',
                'api.allorigins.win'
            ];
            
            for (const domain of domains) {
                try {
                    const start = Date.now();
                    await fetch(`https://${domain}`, { method: 'HEAD', mode: 'no-cors' });
                    const time = Date.now() - start;
                    log(`${domain} DNS解析成功 (${time}ms)`, 'success');
                } catch (error) {
                    log(`${domain} DNS解析失败`, 'error');
                }
            }
        }

        async function testFirewall() {
            log('检测防火墙和网络限制...', 'info');
            
            // 测试常见被阻止的端口和协议
            const testUrls = [
                'https://httpbin.org/status/200',
                'https://httpbin.org/headers',
                'https://api.github.com/rate_limit',
                'https://jsonplaceholder.typicode.com/posts/1'
            ];
            
            let successCount = 0;
            
            for (const url of testUrls) {
                try {
                    const response = await fetch(url);
                    if (response.ok) {
                        successCount++;
                        log(`${url} - 可访问`, 'success');
                    } else {
                        log(`${url} - 被阻止 (${response.status})`, 'warning');
                    }
                } catch (error) {
                    log(`${url} - 连接失败`, 'error');
                }
            }
            
            const percentage = (successCount / testUrls.length) * 100;
            if (percentage < 50) {
                log('检测到严格的网络限制', 'error');
            } else if (percentage < 80) {
                log('检测到部分网络限制', 'warning');
            } else {
                log('网络环境相对开放', 'success');
            }
        }

        function generateSolutions() {
            log('=== 解决方案分析 ===', 'info');
            
            log('基于测试结果，推荐以下解决方案:', 'info');
            
            log('1. 🔧 技术解决方案:', 'info');
            log('   • 使用服务器端代理 (推荐)', 'info');
            log('   • 部署到支持CORS的平台', 'info');
            log('   • 使用Vercel/Netlify Functions', 'info');
            
            log('2. 🌐 网络解决方案:', 'info');
            log('   • 更换网络环境 (如使用移动热点)', 'info');
            log('   • 使用VPN服务', 'info');
            log('   • 联系网络管理员调整防火墙设置', 'info');
            
            log('3. 💰 API解决方案:', 'info');
            log('   • 验证API Key是否有效', 'info');
            log('   • 检查API配额和限制', 'info');
            log('   • 考虑使用其他AI API提供商', 'info');
            
            log('4. 🔄 备用方案:', 'info');
            log('   • 使用高质量本地数据', 'info');
            log('   • 实现离线模式', 'info');
            log('   • 提供预生成的概念图谱', 'info');
        }

        async function testAlternativeAPIs() {
            log('测试替代API服务...', 'info');
            
            const alternatives = [
                {
                    name: 'OpenAI API',
                    url: 'https://api.openai.com/v1/models',
                    requiresKey: true
                },
                {
                    name: 'Anthropic Claude',
                    url: 'https://api.anthropic.com/v1/messages',
                    requiresKey: true
                },
                {
                    name: 'Cohere API',
                    url: 'https://api.cohere.ai/v1/models',
                    requiresKey: true
                },
                {
                    name: 'Hugging Face',
                    url: 'https://api-inference.huggingface.co/models',
                    requiresKey: false
                }
            ];
            
            for (const api of alternatives) {
                try {
                    log(`测试 ${api.name}...`, 'info');
                    const response = await fetch(api.url, { method: 'HEAD' });
                    log(`${api.name} - 可访问 (${response.status})`, response.ok ? 'success' : 'warning');
                } catch (error) {
                    log(`${api.name} - 无法访问`, 'error');
                }
            }
        }

        // 页面加载时自动运行一些基础检测
        window.addEventListener('load', function() {
            log('🔧 API调试工具已加载', 'success');
            log('💡 建议按顺序运行测试：基础连接 → CORS → API Key → 具体调用方法', 'info');
            log('📋 如需帮助，请运行"生成解决方案"获取详细指导', 'info');
        });
    </script>
</body>
</html>

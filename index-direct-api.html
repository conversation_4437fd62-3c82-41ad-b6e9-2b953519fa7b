<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哲学知识图谱学习网站</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <h1>哲学知识图谱</h1>
            <p>探索哲学概念之间的深层联系</p>
            <div style="background: rgba(255,255,255,0.2); padding: 10px; border-radius: 10px; margin-top: 15px;">
                <small style="color: rgba(255,255,255,0.9);">
                    🤖 直接调用OpenRouter API + Google Gemini 2.5 Pro | 
                    🔄 智能多重调用策略 | 
                    🛡️ 自动回退保护
                </small>
            </div>
        </header>

        <!-- 搜索区域 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" id="topicInput" placeholder="输入哲学主题，如：存在主义、道德哲学、自由意志..." />
                <button id="analyzeBtn">生成图谱</button>
                <button id="testApiBtn" style="margin-left: 10px; background: #43e97b;">测试API</button>
            </div>
            <div style="text-align: center; margin-top: 10px; color: rgba(255,255,255,0.8); font-size: 14px;">
                💡 请先点击"测试API"确认连接状态
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 图谱可视化区域 -->
            <div class="graph-container">
                <div id="graph-canvas">
                    <div class="loading-state" id="loadingState">
                        <div class="spinner"></div>
                        <p>AI正在分析概念关系...</p>
                    </div>
                    <div class="empty-state" id="emptyState">
                        <div class="icon">🧠</div>
                        <h3>开始探索哲学世界</h3>
                        <p>在上方输入框中输入感兴趣的哲学主题</p>
                        <div style="margin-top: 20px; color: #888; font-size: 14px;">
                            <p><strong>支持的调用方式：</strong></p>
                            <p>• 直接API调用</p>
                            <p>• CORS代理调用</p>
                            <p>• 多重备用代理</p>
                            <p>• 本地数据回退</p>
                        </div>
                    </div>
                    <svg id="graph-svg"></svg>
                </div>
                
                <!-- 图谱控制工具栏 -->
                <div class="graph-controls" id="graphControls" style="display: none;">
                    <button id="zoomIn" title="放大">+</button>
                    <button id="zoomOut" title="缩小">-</button>
                    <button id="resetView" title="重置视图">⌂</button>
                    <button id="fullscreen" title="全屏">⛶</button>
                </div>
            </div>

            <!-- 侧边栏详情 -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3 id="conceptTitle">概念详情</h3>
                    <button id="closeSidebar">×</button>
                </div>
                <div class="sidebar-content">
                    <div class="concept-info" id="conceptInfo">
                        <div class="concept-description">
                            <h4>概念定义</h4>
                            <p id="conceptDescription">选择图谱中的节点查看详细信息</p>
                        </div>
                        
                        <div class="key-thinkers">
                            <h4>代表人物</h4>
                            <ul id="thinkersList"></ul>
                        </div>
                        
                        <div class="important-works">
                            <h4>重要著作</h4>
                            <ul id="worksList"></ul>
                        </div>
                        
                        <div class="related-concepts">
                            <h4>相关概念</h4>
                            <div id="relatedConcepts"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>基于多重API调用策略的哲学概念分析与可视化平台</p>
        </footer>
    </div>

    <!-- 引入D3.js用于图谱可视化 -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="app-direct-api.js"></script>
</body>
</html>

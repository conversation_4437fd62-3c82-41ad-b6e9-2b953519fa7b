class PhilosophyKnowledgeGraph {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.zoom = null;
        this.selectedNode = null;
        
        // 直接使用OpenRouter API（通过CORS代理）
        this.apiConfig = {
            baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
            apiKey: '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',
            model: 'google/gemini-2.5-pro',
            corsProxy: 'https://cors-anywhere.herokuapp.com/' // CORS代理
        };
        
        console.log('API配置:', this.apiConfig);
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupGraph();
        
        // 显示API配置信息
        this.showApiInfo();
    }

    showApiInfo() {
        const infoDiv = document.createElement('div');
        infoDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        `;
        infoDiv.innerHTML = `
            <strong>API状态</strong><br>
            模型: ${this.apiConfig.model}<br>
            代理: CORS Anywhere<br>
            <button onclick="this.parentElement.remove()" style="margin-top: 5px; background: #ff4444; color: white; border: none; padding: 2px 8px; border-radius: 3px; cursor: pointer;">关闭</button>
        `;
        document.body.appendChild(infoDiv);
    }

    setupEventListeners() {
        // 搜索按钮事件
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzePhilosophyTopic();
        });

        // 测试API按钮事件
        document.getElementById('testApiBtn').addEventListener('click', () => {
            this.testApiConnection();
        });

        // 回车键搜索
        document.getElementById('topicInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.analyzePhilosophyTopic();
            }
        });

        // 侧边栏关闭
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 图谱控制按钮
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('resetView').addEventListener('click', () => this.resetView());
        document.getElementById('fullscreen').addEventListener('click', () => this.toggleFullscreen());

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.updateGraphSize();
        });
    }

    setupGraph() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg = d3.select('#graph-svg')
            .attr('width', this.width)
            .attr('height', this.height);

        // 创建缩放行为
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 3])
            .on('zoom', (event) => {
                this.svg.select('g').attr('transform', event.transform);
            });

        this.svg.call(this.zoom);

        // 创建主容器组
        this.svg.append('g');
    }

    updateGraphSize() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .alpha(0.3)
                .restart();
        }
    }

    async analyzePhilosophyTopic() {
        const topic = document.getElementById('topicInput').value.trim();
        if (!topic) {
            alert('请输入哲学主题');
            return;
        }

        this.showLoading();
        
        try {
            console.log('开始分析主题:', topic);
            
            // 使用AI分析哲学主题
            const conceptData = await this.analyzeTopicWithAI(topic);
            
            console.log('AI分析成功，渲染图谱');
            
            // 渲染图谱
            this.renderGraph(conceptData);
            
            this.hideLoading();
            this.showGraphControls();
            
        } catch (error) {
            console.error('分析失败:', error);
            this.hideLoading();
            
            // 显示具体的错误信息给用户
            const useLocalData = confirm(`AI分析失败: ${error.message}\n\n是否使用本地数据作为备选方案？\n\n点击"确定"使用本地数据，"取消"重试`);
            
            if (useLocalData) {
                // 如果用户选择使用本地数据
                console.log('使用本地数据');
                const fallbackData = this.generateConceptData(topic);
                this.renderGraph(fallbackData);
                this.hideLoading();
                this.showGraphControls();
            }
        }
    }

    async analyzeTopicWithAI(topic) {
        console.log(`开始AI分析主题: ${topic}`);
        
        const prompt = `请分析哲学主题"${topic}"，生成一个知识图谱的节点和连接数据。

要求：
1. 识别5-12个相关的核心概念、子概念、重要思想家
2. 分析概念间的逻辑关系和重要程度
3. 严格按照以下JSON格式返回，不要包含任何其他文字：

{
  "nodes": [
    {
      "id": "unique-id",
      "name": "概念名称",
      "type": "main|core|concept|related|person",
      "importance": 100,
      "description": "概念详细描述",
      "thinkers": ["相关思想家1", "相关思想家2"],
      "works": ["重要著作1", "重要著作2"]
    }
  ],
  "links": [
    {
      "source": "source-id",
      "target": "target-id",
      "strength": 0.9
    }
  ]
}

节点类型说明：
- main: 主要主题概念 (importance: 100)
- core: 核心概念 (importance: 70-90)
- concept: 一般概念 (importance: 50-70)
- related: 相关理论 (importance: 40-60)
- person: 重要思想家 (importance: 60-90)

连接强度范围：0.3-1.0，表示概念间关联程度。`;

        // 方法1：尝试直接调用（可能被CORS阻止）
        try {
            console.log('尝试直接调用OpenRouter API...');
            const response = await this.callOpenRouterAPI(prompt, false);
            return response;
        } catch (directError) {
            console.log('直接调用失败，尝试使用CORS代理:', directError.message);
            
            // 方法2：使用CORS代理
            try {
                const response = await this.callOpenRouterAPI(prompt, true);
                return response;
            } catch (proxyError) {
                console.log('CORS代理调用失败，尝试其他代理:', proxyError.message);
                
                // 方法3：使用其他CORS代理
                const alternativeProxies = [
                    'https://api.allorigins.win/raw?url=',
                    'https://cors-proxy.htmldriven.com/?url='
                ];
                
                for (const proxy of alternativeProxies) {
                    try {
                        console.log('尝试代理:', proxy);
                        const response = await this.callOpenRouterAPIWithProxy(prompt, proxy);
                        return response;
                    } catch (e) {
                        console.log('代理失败:', e.message);
                    }
                }
                
                throw new Error('所有API调用方法都失败了，请检查网络连接和API配置');
            }
        }
    }

    async callOpenRouterAPI(prompt, useProxy = false) {
        const url = useProxy ? 
            `${this.apiConfig.corsProxy}${this.apiConfig.baseUrl}` : 
            this.apiConfig.baseUrl;
            
        console.log('调用API URL:', url);
        
        const requestBody = {
            model: this.apiConfig.model,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 4000
        };
        
        console.log('请求体:', requestBody);
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': '哲学知识图谱学习网站'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('API响应状态:', response.status, response.statusText);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API错误响应:', errorText);
            throw new Error(`API请求失败: ${response.status} ${response.statusText}\n${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
        console.log('API返回数据:', data);
        
        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('API响应格式错误：缺少choices或message字段');
        }

        const content = data.choices[0].message.content;
        console.log('AI生成内容:', content);
        
        return this.parseAIResponse(content);
    }

    async callOpenRouterAPIWithProxy(prompt, proxyUrl) {
        const targetUrl = encodeURIComponent(this.apiConfig.baseUrl);
        const url = `${proxyUrl}${targetUrl}`;
        
        const requestBody = {
            model: this.apiConfig.model,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.7,
            max_tokens: 4000
        };
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            throw new Error(`代理请求失败: ${response.status}`);
        }

        const data = await response.json();
        const content = data.choices[0].message.content;
        
        return this.parseAIResponse(content);
    }

    parseAIResponse(content) {
        try {
            // 清理可能的markdown代码块标记
            let cleanContent = content.trim();
            
            // 移除可能的markdown代码块标记
            if (cleanContent.includes('```json')) {
                cleanContent = cleanContent.replace(/```json\s*\n?/g, '').replace(/\n?```\s*$/g, '');
            } else if (cleanContent.includes('```')) {
                cleanContent = cleanContent.replace(/```\s*\n?/g, '').replace(/\n?```\s*$/g, '');
            }
            
            // 查找JSON开始和结束位置
            const jsonStart = cleanContent.indexOf('{');
            const jsonEnd = cleanContent.lastIndexOf('}') + 1;
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                cleanContent = cleanContent.substring(jsonStart, jsonEnd);
            }
            
            console.log('清理后的内容:', cleanContent);
            
            // 尝试解析JSON响应
            const conceptData = JSON.parse(cleanContent);
            console.log('解析后的概念数据:', conceptData);
            
            // 验证数据格式
            if (!conceptData.nodes || !conceptData.links || !Array.isArray(conceptData.nodes) || !Array.isArray(conceptData.links)) {
                throw new Error('数据格式不正确：缺少nodes或links数组');
            }

            if (conceptData.nodes.length === 0) {
                throw new Error('数据格式不正确：nodes数组为空');
            }

            // 为节点添加详细信息存储
            conceptData.nodes.forEach(node => {
                if (!node.description) node.description = `${node.name}是重要的哲学概念。`;
                if (!node.thinkers) node.thinkers = ['相关哲学家'];
                if (!node.works) node.works = ['相关著作'];
                if (!node.id) node.id = node.name.replace(/\s+/g, '-').toLowerCase();
                if (!node.type) node.type = 'concept';
                if (!node.importance) node.importance = 50;
            });

            console.log('AI分析成功，返回数据');
            return conceptData;
            
        } catch (parseError) {
            console.error('JSON解析失败:', parseError);
            console.log('原始AI返回内容:', content);
            throw new Error(`AI返回的数据格式无法解析: ${parseError.message}\n原始内容: ${content.substring(0, 200)}...`);
        }
    }

    async testApiConnection() {
        console.log('开始测试API连接...');
        
        try {
            const prompt = '请回复"API测试成功"';
            
            // 尝试所有可能的调用方法
            let success = false;
            let result = null;
            
            // 方法1：直接调用
            try {
                console.log('测试直接调用...');
                await this.callOpenRouterAPI(prompt, false);
                success = true;
                result = '直接调用成功';
            } catch (e) {
                console.log('直接调用失败:', e.message);
            }
            
            // 方法2：CORS代理
            if (!success) {
                try {
                    console.log('测试CORS代理...');
                    await this.callOpenRouterAPI(prompt, true);
                    success = true;
                    result = 'CORS代理调用成功';
                } catch (e) {
                    console.log('CORS代理失败:', e.message);
                }
            }
            
            if (success) {
                alert(`🎉 API测试成功!\n\n方法: ${result}\n模型: ${this.apiConfig.model}\n\n现在可以正常使用AI分析功能！`);
            } else {
                alert(`⚠️ API测试失败\n\n所有调用方法都失败了。\n\n可能的原因:\n1. 网络连接问题\n2. API Key无效\n3. CORS策略限制\n\n建议使用本地数据模式。`);
            }
            
        } catch (error) {
            console.error('API测试失败:', error);
            alert(`API测试失败!\n\n错误: ${error.message}\n\n请检查:\n1. 网络连接\n2. API Key有效性\n3. 浏览器控制台的详细错误信息`);
        }
    }

    generateConceptData(topic) {
        // 扩展的本地数据库
        const conceptMaps = {
            '存在主义': {
                nodes: [
                    { id: 'existentialism', name: '存在主义', type: 'main', importance: 100,
                      description: '20世纪哲学流派，强调个体存在的独特性和主观体验，认为存在先于本质。',
                      thinkers: ['萨特', '加缪', '克尔凯郭尔', '海德格尔'],
                      works: ['《存在与虚无》', '《局外人》', '《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'authentic-existence', name: '真实存在', type: 'core', importance: 85,
                      description: '指个体摆脱社会角色束缚，以真诚方式面对自己存在状态的生活方式。',
                      thinkers: ['海德格尔', '萨特'],
                      works: ['《存在与时间》', '《存在与虚无》'] },
                    { id: 'bad-faith', name: '恶劣信念', type: 'core', importance: 80,
                      description: '萨特提出的概念，指个体逃避自由和责任，假装自己没有选择的自欺行为。',
                      thinkers: ['萨特'],
                      works: ['《存在与虚无》'] },
                    { id: 'freedom', name: '自由', type: 'core', importance: 90,
                      description: '存在主义核心概念，人被判定是自由的，必须承担选择的全部责任。',
                      thinkers: ['萨特', '加缪'],
                      works: ['《存在与虚无》', '《西西弗的神话》'] },
                    { id: 'anxiety', name: '焦虑', type: 'concept', importance: 70,
                      description: '面对存在的无意义和自由选择时产生的根本性情绪体验。',
                      thinkers: ['克尔凯郭尔', '海德格尔'],
                      works: ['《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'absurd', name: '荒诞', type: 'related', importance: 75,
                      description: '加缪哲学的核心，指人类寻求意义与世界无意义之间的冲突。',
                      thinkers: ['加缪'],
                      works: ['《西西弗的神话》', '《局外人》'] }
                ],
                links: [
                    { source: 'existentialism', target: 'authentic-existence', strength: 0.9 },
                    { source: 'existentialism', target: 'bad-faith', strength: 0.8 },
                    { source: 'existentialism', target: 'freedom', strength: 0.95 },
                    { source: 'authentic-existence', target: 'bad-faith', strength: 0.7 },
                    { source: 'freedom', target: 'anxiety', strength: 0.8 },
                    { source: 'absurd', target: 'existentialism', strength: 0.6 }
                ]
            }
        };

        return conceptMaps[topic] || this.generateGenericConceptData(topic);
    }

    generateGenericConceptData(topic) {
        return {
            nodes: [
                { id: 'main', name: topic, type: 'main', importance: 100,
                  description: `${topic}是哲学的重要领域。`,
                  thinkers: ['相关哲学家'],
                  works: ['相关著作'] },
                { id: 'concept1', name: '核心概念1', type: 'core', importance: 80,
                  description: `${topic}的核心概念。`,
                  thinkers: ['哲学家A'],
                  works: ['著作1'] },
                { id: 'concept2', name: '核心概念2', type: 'core', importance: 75,
                  description: `${topic}的重要概念。`,
                  thinkers: ['哲学家B'],
                  works: ['著作2'] }
            ],
            links: [
                { source: 'main', target: 'concept1', strength: 0.9 },
                { source: 'main', target: 'concept2', strength: 0.8 }
            ]
        };
    }

    renderGraph(data) {
        this.nodes = data.nodes;
        this.links = data.links;

        // 清除之前的图形
        this.svg.select('g').selectAll('*').remove();

        // 创建力导向图模拟
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 120 - d.strength * 60))
            .force('charge', d3.forceManyBody().strength(-400))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 10));

        const g = this.svg.select('g');

        // 创建连线
        const link = g.append('g')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .style('stroke-width', d => d.strength * 4);

        // 创建节点组
        const node = g.append('g')
            .selectAll('.node-group')
            .data(this.nodes)
            .enter().append('g')
            .attr('class', 'node-group')
            .style('cursor', 'pointer')
            .call(d3.drag()
                .on('start', (event, d) => this.dragstarted(event, d))
                .on('drag', (event, d) => this.dragged(event, d))
                .on('end', (event, d) => this.dragended(event, d)));

        // 添加节点圆圈
        node.append('circle')
            .attr('class', 'node')
            .attr('r', d => this.getNodeRadius(d))
            .style('fill', d => this.getNodeColor(d))
            .style('stroke', '#fff')
            .style('stroke-width', 2)
            .on('click', (event, d) => this.nodeClicked(event, d))
            .on('mouseover', (event, d) => this.nodeMouseOver(event, d))
            .on('mouseout', (event, d) => this.nodeMouseOut(event, d));

        // 添加节点标签
        node.append('text')
            .attr('class', 'node-label')
            .attr('dy', d => this.getNodeRadius(d) + 15)
            .style('font-size', d => d.type === 'main' ? '14px' : '12px')
            .style('font-weight', d => d.type === 'main' ? 'bold' : 'normal')
            .text(d => d.name);

        // 更新位置
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });

        // 显示SVG
        document.getElementById('graph-svg').style.display = 'block';
    }

    getNodeRadius(node) {
        const baseRadius = 12;
        const maxRadius = 30;
        return baseRadius + (node.importance / 100) * (maxRadius - baseRadius);
    }

    getNodeColor(node) {
        const colors = {
            'main': '#667eea',
            'core': '#764ba2',
            'concept': '#f093fb',
            'related': '#4facfe',
            'person': '#43e97b'
        };
        return colors[node.type] || '#999';
    }

    nodeClicked(event, node) {
        this.selectedNode = node;
        this.showNodeDetails(node);
        this.highlightConnections(node);
    }

    nodeMouseOver(event, node) {
        d3.select(event.target)
            .style('stroke-width', '4px')
            .style('stroke', '#333')
            .transition()
            .duration(200)
            .attr('r', this.getNodeRadius(node) + 3);
    }

    nodeMouseOut(event, node) {
        if (this.selectedNode !== node) {
            d3.select(event.target)
                .style('stroke-width', '2px')
                .style('stroke', '#fff')
                .transition()
                .duration(200)
                .attr('r', this.getNodeRadius(node));
        }
    }

    highlightConnections(node) {
        // 重置所有连线样式
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.3);

        // 高亮相关连线
        this.svg.selectAll('.link')
            .filter(d => d.source.id === node.id || d.target.id === node.id)
            .classed('highlighted', true)
            .style('opacity', 1);

        // 高亮相关节点
        this.svg.selectAll('.node')
            .style('opacity', 0.3);

        this.svg.selectAll('.node')
            .filter(d => {
                if (d.id === node.id) return true;
                return this.links.some(link => 
                    (link.source.id === node.id && link.target.id === d.id) ||
                    (link.target.id === node.id && link.source.id === d.id)
                );
            })
            .style('opacity', 1);
    }

    showNodeDetails(node) {
        const sidebar = document.getElementById('sidebar');
        const conceptTitle = document.getElementById('conceptTitle');
        const conceptDescription = document.getElementById('conceptDescription');
        const thinkersList = document.getElementById('thinkersList');
        const worksList = document.getElementById('worksList');
        const relatedConcepts = document.getElementById('relatedConcepts');

        // 设置标题
        conceptTitle.textContent = node.name;

        // 设置描述
        conceptDescription.textContent = node.description || `${node.name}是重要的哲学概念。`;

        // 设置代表人物
        thinkersList.innerHTML = '';
        (node.thinkers || ['相关哲学家']).forEach(thinker => {
            const li = document.createElement('li');
            li.textContent = thinker;
            thinkersList.appendChild(li);
        });

        // 设置重要著作
        worksList.innerHTML = '';
        (node.works || ['相关著作']).forEach(work => {
            const li = document.createElement('li');
            li.textContent = work;
            worksList.appendChild(li);
        });

        // 设置相关概念
        relatedConcepts.innerHTML = '';
        const relatedNodes = this.getRelatedNodes(node);
        relatedNodes.forEach(relatedNode => {
            const tag = document.createElement('div');
            tag.className = 'related-tag';
            tag.textContent = relatedNode.name;
            tag.addEventListener('click', () => {
                this.nodeClicked(null, relatedNode);
            });
            relatedConcepts.appendChild(tag);
        });

        // 显示侧边栏
        sidebar.classList.add('active');
    }

    getRelatedNodes(node) {
        return this.links
            .filter(link => link.source.id === node.id || link.target.id === node.id)
            .map(link => link.source.id === node.id ? link.target : link.source)
            .slice(0, 6);
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('active');
        this.selectedNode = null;
        
        // 重置所有样式
        this.svg.selectAll('.node')
            .style('stroke-width', '2px')
            .style('stroke', '#fff')
            .style('opacity', 1);
        
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.6);
    }

    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    zoomIn() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 1.5);
    }

    zoomOut() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 0.75);
    }

    resetView() {
        this.svg.transition().duration(500).call(this.zoom.transform, d3.zoomIdentity);
    }

    toggleFullscreen() {
        const container = document.getElementById('graph-canvas');
        if (!document.fullscreenElement) {
            container.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showLoading() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('analyzeBtn').disabled = true;
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('analyzeBtn').disabled = false;
    }

    showGraphControls() {
        document.getElementById('graphControls').style.display = 'flex';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new PhilosophyKnowledgeGraph();
});

class PhilosophyKnowledgeGraph {
    constructor() {
        this.svg = null;
        this.simulation = null;
        this.nodes = [];
        this.links = [];
        this.width = 0;
        this.height = 0;
        this.zoom = null;
        this.selectedNode = null;
        
        // 使用可靠的CORS代理服务
        this.apiConfig = {
            baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
            apiKey: '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',
            model: 'google/gemini-2.5-pro',
            // 使用多个可靠的CORS代理
            corsProxies: [
                'https://api.allorigins.win/raw?url=',
                'https://cors-proxy.htmldriven.com/?url=',
                'https://thingproxy.freeboard.io/fetch/'
            ]
        };
        
        this.currentProxyIndex = 0;
        this.apiWorking = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupGraph();
        this.showWelcomeMessage();
    }

    showWelcomeMessage() {
        // 显示欢迎信息
        const welcomeDiv = document.createElement('div');
        welcomeDiv.id = 'welcome-info';
        welcomeDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            z-index: 1000;
            max-width: 350px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        `;
        welcomeDiv.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>🚀 API配置完成</strong>
                <button onclick="document.getElementById('welcome-info').remove()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 2px 8px; border-radius: 3px; cursor: pointer;">×</button>
            </div>
            <div>
                <div>📡 OpenRouter + Gemini 2.5 Pro</div>
                <div>🔄 智能CORS代理切换</div>
                <div>🛡️ 多重错误处理机制</div>
                <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.3);">
                    <button onclick="window.philosophyGraph.testAllMethods()" style="background: #43e97b; border: none; color: white; padding: 8px 12px; border-radius: 5px; cursor: pointer; width: 100%;">🔧 测试所有API方法</button>
                </div>
            </div>
        `;
        document.body.appendChild(welcomeDiv);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (document.getElementById('welcome-info')) {
                document.getElementById('welcome-info').style.opacity = '0';
                setTimeout(() => {
                    const elem = document.getElementById('welcome-info');
                    if (elem) elem.remove();
                }, 500);
            }
        }, 5000);
    }

    setupEventListeners() {
        // 搜索按钮事件
        document.getElementById('analyzeBtn').addEventListener('click', () => {
            this.analyzePhilosophyTopic();
        });

        // 测试API按钮事件
        document.getElementById('testApiBtn').addEventListener('click', () => {
            this.testAllMethods();
        });

        // 回车键搜索
        document.getElementById('topicInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.analyzePhilosophyTopic();
            }
        });

        // 侧边栏关闭
        document.getElementById('closeSidebar').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 图谱控制按钮
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        document.getElementById('resetView').addEventListener('click', () => this.resetView());
        document.getElementById('fullscreen').addEventListener('click', () => this.toggleFullscreen());

        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.updateGraphSize();
        });
    }

    setupGraph() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg = d3.select('#graph-svg')
            .attr('width', this.width)
            .attr('height', this.height);

        // 创建缩放行为
        this.zoom = d3.zoom()
            .scaleExtent([0.3, 3])
            .on('zoom', (event) => {
                this.svg.select('g').attr('transform', event.transform);
            });

        this.svg.call(this.zoom);

        // 创建主容器组
        this.svg.append('g');
    }

    updateGraphSize() {
        const container = document.getElementById('graph-canvas');
        const rect = container.getBoundingClientRect();
        this.width = rect.width;
        this.height = rect.height;

        this.svg
            .attr('width', this.width)
            .attr('height', this.height);

        if (this.simulation) {
            this.simulation
                .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                .alpha(0.3)
                .restart();
        }
    }

    async analyzePhilosophyTopic() {
        const topic = document.getElementById('topicInput').value.trim();
        if (!topic) {
            alert('请输入哲学主题');
            return;
        }

        this.showLoading();
        
        try {
            console.log('🧠 开始AI分析主题:', topic);
            
            // 使用AI分析哲学主题
            const conceptData = await this.analyzeTopicWithAI(topic);
            
            console.log('✅ AI分析成功，渲染图谱');
            
            // 渲染图谱
            this.renderGraph(conceptData);
            
            this.hideLoading();
            this.showGraphControls();
            
            // 显示成功提示
            this.showSuccessMessage('🎉 AI分析完成！', `成功生成 ${conceptData.nodes.length} 个概念节点`);
            
        } catch (error) {
            console.error('❌ AI分析失败:', error);
            this.hideLoading();
            
            // 友好的错误处理
            const useLocalData = confirm(
                `🤖 AI分析遇到问题：${error.message}\n\n` +
                `💡 我们为您准备了高质量的本地哲学数据库作为备选方案。\n\n` +
                `点击"确定"使用本地数据，"取消"重试API调用。`
            );
            
            if (useLocalData) {
                console.log('📚 使用本地哲学数据库');
                const fallbackData = this.generateConceptData(topic);
                this.renderGraph(fallbackData);
                this.hideLoading();
                this.showGraphControls();
                this.showSuccessMessage('📚 本地数据已加载', '使用精心整理的哲学概念数据');
            }
        }
    }

    async analyzeTopicWithAI(topic) {
        console.log(`🔍 开始AI分析: ${topic}`);
        
        const prompt = `请分析哲学主题"${topic}"，生成知识图谱数据。

要求：
1. 识别6-10个核心概念、相关理论、重要思想家
2. 分析概念间的逻辑关系和重要程度
3. 严格按照JSON格式返回，不要其他文字：

{
  "nodes": [
    {
      "id": "unique-id",
      "name": "概念名称",
      "type": "main|core|concept|related|person",
      "importance": 100,
      "description": "详细描述",
      "thinkers": ["思想家1", "思想家2"],
      "works": ["著作1", "著作2"]
    }
  ],
  "links": [
    {
      "source": "source-id",
      "target": "target-id",
      "strength": 0.9
    }
  ]
}

类型说明：
- main: 主题 (importance: 100)
- core: 核心概念 (70-90)
- concept: 一般概念 (50-70)
- related: 相关理论 (40-60)
- person: 思想家 (60-90)

连接强度：0.3-1.0`;

        // 尝试多种API调用方法
        const methods = [
            () => this.directAPICall(prompt),
            () => this.proxyAPICall(prompt, 0),
            () => this.proxyAPICall(prompt, 1),
            () => this.proxyAPICall(prompt, 2),
            () => this.fallbackAPICall(prompt)
        ];

        let lastError = null;
        
        for (let i = 0; i < methods.length; i++) {
            try {
                console.log(`🔄 尝试方法 ${i + 1}/${methods.length}`);
                const result = await methods[i]();
                console.log(`✅ 方法 ${i + 1} 成功`);
                this.apiWorking = true;
                return result;
            } catch (error) {
                console.log(`❌ 方法 ${i + 1} 失败:`, error.message);
                lastError = error;
                // 短暂延迟后尝试下一种方法
                if (i < methods.length - 1) {
                    await this.delay(500);
                }
            }
        }
        
        throw new Error(`所有API调用方法都失败了。最后错误: ${lastError?.message || '未知错误'}`);
    }

    async directAPICall(prompt) {
        console.log('📡 尝试直接API调用...');
        
        const response = await fetch(this.apiConfig.baseUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': window.location.origin,
                'X-Title': '哲学知识图谱'
            },
            body: JSON.stringify({
                model: this.apiConfig.model,
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.7,
                max_tokens: 4000
            })
        });

        return await this.processAPIResponse(response);
    }

    async proxyAPICall(prompt, proxyIndex) {
        const proxy = this.apiConfig.corsProxies[proxyIndex];
        console.log(`🔄 尝试CORS代理 ${proxyIndex + 1}: ${proxy}`);
        
        const targetUrl = encodeURIComponent(this.apiConfig.baseUrl);
        const proxyUrl = `${proxy}${targetUrl}`;
        
        const response = await fetch(proxyUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.apiConfig.model,
                messages: [{ role: 'user', content: prompt }],
                temperature: 0.7,
                max_tokens: 4000
            })
        });

        return await this.processAPIResponse(response);
    }

    async fallbackAPICall(prompt) {
        console.log('🆘 尝试备用API调用方式...');
        
        // 使用jsonp方式或其他备用方法
        const response = await fetch(`https://api.allorigins.win/get?url=${encodeURIComponent(this.apiConfig.baseUrl)}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                url: this.apiConfig.baseUrl,
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.apiConfig.model,
                    messages: [{ role: 'user', content: prompt }],
                    temperature: 0.7,
                    max_tokens: 4000
                })
            })
        });

        if (!response.ok) {
            throw new Error(`备用API调用失败: ${response.status}`);
        }

        const data = await response.json();
        const apiResponse = JSON.parse(data.contents);
        
        if (!apiResponse.choices || !apiResponse.choices[0]) {
            throw new Error('备用API响应格式错误');
        }

        return this.parseAIContent(apiResponse.choices[0].message.content);
    }

    async processAPIResponse(response) {
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API调用失败: ${response.status} ${response.statusText}\n${errorText.substring(0, 200)}`);
        }

        const data = await response.json();
        
        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('API响应格式错误：缺少必要字段');
        }

        const content = data.choices[0].message.content;
        return this.parseAIContent(content);
    }

    parseAIContent(content) {
        console.log('🔍 解析AI响应内容...');
        
        try {
            // 清理内容
            let cleanContent = content.trim();
            
            // 移除markdown标记
            cleanContent = cleanContent.replace(/```json\s*\n?/g, '').replace(/\n?```\s*$/g, '');
            cleanContent = cleanContent.replace(/```\s*\n?/g, '').replace(/\n?```\s*$/g, '');
            
            // 查找JSON部分
            const jsonStart = cleanContent.indexOf('{');
            const jsonEnd = cleanContent.lastIndexOf('}') + 1;
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                cleanContent = cleanContent.substring(jsonStart, jsonEnd);
            }
            
            console.log('📝 清理后的内容:', cleanContent.substring(0, 200) + '...');
            
            // 解析JSON
            const conceptData = JSON.parse(cleanContent);
            
            // 验证数据结构
            if (!conceptData.nodes || !Array.isArray(conceptData.nodes) || conceptData.nodes.length === 0) {
                throw new Error('无效的节点数据');
            }
            
            if (!conceptData.links || !Array.isArray(conceptData.links)) {
                conceptData.links = [];
            }

            // 补全节点信息
            conceptData.nodes.forEach(node => {
                if (!node.id) node.id = node.name.replace(/[^\w\u4e00-\u9fff]/g, '-').toLowerCase();
                if (!node.description) node.description = `${node.name}是重要的哲学概念。`;
                if (!node.thinkers) node.thinkers = ['相关哲学家'];
                if (!node.works) node.works = ['相关著作'];
                if (!node.type) node.type = 'concept';
                if (!node.importance) node.importance = Math.floor(Math.random() * 30) + 50;
            });

            console.log('✅ 成功解析概念数据:', conceptData);
            return conceptData;
            
        } catch (parseError) {
            console.error('❌ JSON解析失败:', parseError);
            throw new Error(`无法解析AI返回的数据: ${parseError.message}`);
        }
    }

    async testAllMethods() {
        console.log('🧪 开始测试所有API方法...');
        
        const testPrompt = '请简单回复"测试成功"';
        const results = [];
        
        // 显示测试进度
        this.showTestProgress();
        
        const methods = [
            { name: '直接API调用', fn: () => this.directAPICall(testPrompt) },
            { name: 'CORS代理1', fn: () => this.proxyAPICall(testPrompt, 0) },
            { name: 'CORS代理2', fn: () => this.proxyAPICall(testPrompt, 1) },
            { name: 'CORS代理3', fn: () => this.proxyAPICall(testPrompt, 2) },
            { name: '备用方法', fn: () => this.fallbackAPICall(testPrompt) }
        ];

        for (let i = 0; i < methods.length; i++) {
            const method = methods[i];
            this.updateTestProgress(i + 1, methods.length, method.name);
            
            try {
                await method.fn();
                results.push({ name: method.name, status: '✅ 成功' });
                console.log(`✅ ${method.name}: 成功`);
            } catch (error) {
                results.push({ name: method.name, status: '❌ 失败', error: error.message });
                console.log(`❌ ${method.name}: ${error.message}`);
            }
            
            await this.delay(1000);
        }
        
        this.hideTestProgress();
        this.showTestResults(results);
    }

    showTestProgress() {
        const progressDiv = document.createElement('div');
        progressDiv.id = 'test-progress';
        progressDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 2000;
            text-align: center;
            min-width: 300px;
        `;
        progressDiv.innerHTML = `
            <div style="margin-bottom: 20px;">
                <div style="font-size: 2rem; margin-bottom: 10px;">🔧</div>
                <h3>测试API连接</h3>
            </div>
            <div id="progress-info">准备中...</div>
            <div style="margin-top: 20px;">
                <div style="background: #f0f0f0; height: 10px; border-radius: 5px; overflow: hidden;">
                    <div id="progress-bar" style="background: linear-gradient(45deg, #667eea, #764ba2); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>
        `;
        document.body.appendChild(progressDiv);
    }

    updateTestProgress(current, total, methodName) {
        const progressInfo = document.getElementById('progress-info');
        const progressBar = document.getElementById('progress-bar');
        
        if (progressInfo) {
            progressInfo.textContent = `测试 ${methodName} (${current}/${total})`;
        }
        
        if (progressBar) {
            progressBar.style.width = `${(current / total) * 100}%`;
        }
    }

    hideTestProgress() {
        const progressDiv = document.getElementById('test-progress');
        if (progressDiv) {
            progressDiv.remove();
        }
    }

    showTestResults(results) {
        const successfulMethods = results.filter(r => r.status.includes('成功'));
        const failedMethods = results.filter(r => r.status.includes('失败'));
        
        let message = '🔧 API连接测试完成\n\n';
        
        if (successfulMethods.length > 0) {
            message += `✅ 可用方法 (${successfulMethods.length}个):\n`;
            successfulMethods.forEach(m => {
                message += `• ${m.name}\n`;
            });
            message += '\n';
        }
        
        if (failedMethods.length > 0) {
            message += `❌ 不可用方法 (${failedMethods.length}个):\n`;
            failedMethods.forEach(m => {
                message += `• ${m.name}\n`;
            });
        }
        
        if (successfulMethods.length > 0) {
            message += '\n🎉 恭喜！至少有一种方法可以连接API。\n现在可以正常使用AI分析功能了！';
            this.apiWorking = true;
        } else {
            message += '\n⚠️ 所有API连接方法都失败了。\n建议使用本地数据模式继续体验。';
            this.apiWorking = false;
        }
        
        alert(message);
    }

    showSuccessMessage(title, description) {
        const messageDiv = document.createElement('div');
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(45deg, #43e97b, #52c985);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            z-index: 1500;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            text-align: center;
            animation: slideDown 0.3s ease;
        `;
        messageDiv.innerHTML = `
            <style>
                @keyframes slideDown {
                    from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                    to { transform: translateX(-50%) translateY(0); opacity: 1; }
                }
            </style>
            <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
            <div style="font-size: 14px; opacity: 0.9;">${description}</div>
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            setTimeout(() => messageDiv.remove(), 300);
        }, 3000);
    }

    generateConceptData(topic) {
        // 丰富的本地哲学数据库
        const conceptMaps = {
            '存在主义': {
                nodes: [
                    { id: 'existentialism', name: '存在主义', type: 'main', importance: 100,
                      description: '20世纪哲学流派，强调个体存在的独特性和主观体验，认为存在先于本质。',
                      thinkers: ['萨特', '加缪', '克尔凯郭尔', '海德格尔'],
                      works: ['《存在与虚无》', '《局外人》', '《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'authentic-existence', name: '真实存在', type: 'core', importance: 85,
                      description: '个体摆脱社会角色束缚，以真诚方式面对自己存在状态的生活方式。',
                      thinkers: ['海德格尔', '萨特'],
                      works: ['《存在与时间》', '《存在与虚无》'] },
                    { id: 'bad-faith', name: '恶劣信念', type: 'core', importance: 80,
                      description: '萨特提出的概念，指个体逃避自由和责任，假装自己没有选择的自欺行为。',
                      thinkers: ['萨特'],
                      works: ['《存在与虚无》'] },
                    { id: 'freedom', name: '自由', type: 'core', importance: 90,
                      description: '存在主义核心概念，人被判定是自由的，必须承担选择的全部责任。',
                      thinkers: ['萨特', '加缪'],
                      works: ['《存在与虚无》', '《西西弗的神话》'] },
                    { id: 'anxiety', name: '焦虑', type: 'concept', importance: 70,
                      description: '面对存在的无意义和自由选择时产生的根本性情绪体验。',
                      thinkers: ['克尔凯郭尔', '海德格尔'],
                      works: ['《恐惧与颤栗》', '《存在与时间》'] },
                    { id: 'absurd', name: '荒诞', type: 'related', importance: 75,
                      description: '加缪哲学的核心，指人类寻求意义与世界无意义之间的冲突。',
                      thinkers: ['加缪'],
                      works: ['《西西弗的神话》', '《局外人》'] },
                    { id: 'sartre', name: '萨特', type: 'person', importance: 95,
                      description: '法国哲学家，存在主义主要代表人物。',
                      thinkers: ['萨特'],
                      works: ['《存在与虚无》', '《恶心》'] }
                ],
                links: [
                    { source: 'existentialism', target: 'authentic-existence', strength: 0.9 },
                    { source: 'existentialism', target: 'bad-faith', strength: 0.8 },
                    { source: 'existentialism', target: 'freedom', strength: 0.95 },
                    { source: 'authentic-existence', target: 'bad-faith', strength: 0.7 },
                    { source: 'freedom', target: 'anxiety', strength: 0.8 },
                    { source: 'sartre', target: 'existentialism', strength: 0.9 },
                    { source: 'sartre', target: 'bad-faith', strength: 0.8 },
                    { source: 'absurd', target: 'existentialism', strength: 0.6 }
                ]
            },
            '道德哲学': {
                nodes: [
                    { id: 'ethics', name: '道德哲学', type: 'main', importance: 100,
                      description: '研究道德原则、价值判断和伦理行为的哲学分支。',
                      thinkers: ['亚里士多德', '康德', '密尔'],
                      works: ['《尼各马可伦理学》', '《道德形而上学基础》', '《功利主义》'] },
                    { id: 'virtue-ethics', name: '美德伦理学', type: 'core', importance: 85,
                      description: '强调品格和美德，认为道德行为源于良好的品格特质。',
                      thinkers: ['亚里士多德', '阿奎那'],
                      works: ['《尼各马可伦理学》'] },
                    { id: 'deontology', name: '义务论', type: 'core', importance: 85,
                      description: '康德提出的道德理论，强调行为的动机和义务，而非后果。',
                      thinkers: ['康德'],
                      works: ['《道德形而上学基础》', '《实践理性批判》'] }
                ],
                links: [
                    { source: 'ethics', target: 'virtue-ethics', strength: 0.9 },
                    { source: 'ethics', target: 'deontology', strength: 0.9 }
                ]
            }
        };

        return conceptMaps[topic] || this.generateGenericConceptData(topic);
    }

    generateGenericConceptData(topic) {
        const genericNodes = [
            { id: 'main', name: topic, type: 'main', importance: 100,
              description: `${topic}是哲学的重要研究领域，涉及基本的概念和理论问题。`,
              thinkers: ['相关哲学家'],
              works: ['相关哲学著作'] },
            { id: 'concept1', name: '核心概念A', type: 'core', importance: 80,
              description: `${topic}领域的核心概念之一。`,
              thinkers: ['重要思想家A'],
              works: ['重要著作A'] },
            { id: 'concept2', name: '核心概念B', type: 'core', importance: 75,
              description: `${topic}领域的另一个重要概念。`,
              thinkers: ['重要思想家B'],
              works: ['重要著作B'] },
            { id: 'theory1', name: '相关理论', type: 'related', importance: 65,
              description: `与${topic}相关的重要理论框架。`,
              thinkers: ['理论家'],
              works: ['理论著作'] }
        ];

        return {
            nodes: genericNodes,
            links: [
                { source: 'main', target: 'concept1', strength: 0.9 },
                { source: 'main', target: 'concept2', strength: 0.8 },
                { source: 'concept1', target: 'theory1', strength: 0.7 }
            ]
        };
    }

    renderGraph(data) {
        this.nodes = data.nodes;
        this.links = data.links;

        // 清除之前的图形
        this.svg.select('g').selectAll('*').remove();

        // 创建力导向图模拟
        this.simulation = d3.forceSimulation(this.nodes)
            .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 120 - d.strength * 60))
            .force('charge', d3.forceManyBody().strength(-400))
            .force('center', d3.forceCenter(this.width / 2, this.height / 2))
            .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 10));

        const g = this.svg.select('g');

        // 创建连线
        const link = g.append('g')
            .selectAll('line')
            .data(this.links)
            .enter().append('line')
            .attr('class', 'link')
            .style('stroke-width', d => d.strength * 4);

        // 创建节点组
        const node = g.append('g')
            .selectAll('.node-group')
            .data(this.nodes)
            .enter().append('g')
            .attr('class', 'node-group')
            .style('cursor', 'pointer')
            .call(d3.drag()
                .on('start', (event, d) => this.dragstarted(event, d))
                .on('drag', (event, d) => this.dragged(event, d))
                .on('end', (event, d) => this.dragended(event, d)));

        // 添加节点圆圈
        node.append('circle')
            .attr('class', 'node')
            .attr('r', d => this.getNodeRadius(d))
            .style('fill', d => this.getNodeColor(d))
            .style('stroke', '#fff')
            .style('stroke-width', 2)
            .on('click', (event, d) => this.nodeClicked(event, d))
            .on('mouseover', (event, d) => this.nodeMouseOver(event, d))
            .on('mouseout', (event, d) => this.nodeMouseOut(event, d));

        // 添加节点标签
        node.append('text')
            .attr('class', 'node-label')
            .attr('dy', d => this.getNodeRadius(d) + 15)
            .style('font-size', d => d.type === 'main' ? '14px' : '12px')
            .style('font-weight', d => d.type === 'main' ? 'bold' : 'normal')
            .text(d => d.name);

        // 更新位置
        this.simulation.on('tick', () => {
            link
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y);

            node
                .attr('transform', d => `translate(${d.x},${d.y})`);
        });

        // 显示SVG
        document.getElementById('graph-svg').style.display = 'block';
    }

    getNodeRadius(node) {
        const baseRadius = 12;
        const maxRadius = 30;
        return baseRadius + (node.importance / 100) * (maxRadius - baseRadius);
    }

    getNodeColor(node) {
        const colors = {
            'main': '#667eea',
            'core': '#764ba2',
            'concept': '#f093fb',
            'related': '#4facfe',
            'person': '#43e97b'
        };
        return colors[node.type] || '#999';
    }

    nodeClicked(event, node) {
        this.selectedNode = node;
        this.showNodeDetails(node);
        this.highlightConnections(node);
    }

    nodeMouseOver(event, node) {
        d3.select(event.target)
            .style('stroke-width', '4px')
            .style('stroke', '#333')
            .transition()
            .duration(200)
            .attr('r', this.getNodeRadius(node) + 3);
    }

    nodeMouseOut(event, node) {
        if (this.selectedNode !== node) {
            d3.select(event.target)
                .style('stroke-width', '2px')
                .style('stroke', '#fff')
                .transition()
                .duration(200)
                .attr('r', this.getNodeRadius(node));
        }
    }

    highlightConnections(node) {
        // 重置所有连线样式
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.3);

        // 高亮相关连线
        this.svg.selectAll('.link')
            .filter(d => d.source.id === node.id || d.target.id === node.id)
            .classed('highlighted', true)
            .style('opacity', 1);

        // 高亮相关节点
        this.svg.selectAll('.node')
            .style('opacity', 0.3);

        this.svg.selectAll('.node')
            .filter(d => {
                if (d.id === node.id) return true;
                return this.links.some(link => 
                    (link.source.id === node.id && link.target.id === d.id) ||
                    (link.target.id === node.id && link.source.id === d.id)
                );
            })
            .style('opacity', 1);
    }

    showNodeDetails(node) {
        const sidebar = document.getElementById('sidebar');
        const conceptTitle = document.getElementById('conceptTitle');
        const conceptDescription = document.getElementById('conceptDescription');
        const thinkersList = document.getElementById('thinkersList');
        const worksList = document.getElementById('worksList');
        const relatedConcepts = document.getElementById('relatedConcepts');

        // 设置标题
        conceptTitle.textContent = node.name;

        // 设置描述
        conceptDescription.textContent = node.description || `${node.name}是重要的哲学概念。`;

        // 设置代表人物
        thinkersList.innerHTML = '';
        (node.thinkers || ['相关哲学家']).forEach(thinker => {
            const li = document.createElement('li');
            li.textContent = thinker;
            thinkersList.appendChild(li);
        });

        // 设置重要著作
        worksList.innerHTML = '';
        (node.works || ['相关著作']).forEach(work => {
            const li = document.createElement('li');
            li.textContent = work;
            worksList.appendChild(li);
        });

        // 设置相关概念
        relatedConcepts.innerHTML = '';
        const relatedNodes = this.getRelatedNodes(node);
        relatedNodes.forEach(relatedNode => {
            const tag = document.createElement('div');
            tag.className = 'related-tag';
            tag.textContent = relatedNode.name;
            tag.addEventListener('click', () => {
                this.nodeClicked(null, relatedNode);
            });
            relatedConcepts.appendChild(tag);
        });

        // 显示侧边栏
        sidebar.classList.add('active');
    }

    getRelatedNodes(node) {
        return this.links
            .filter(link => link.source.id === node.id || link.target.id === node.id)
            .map(link => link.source.id === node.id ? link.target : link.source)
            .slice(0, 6);
    }

    closeSidebar() {
        document.getElementById('sidebar').classList.remove('active');
        this.selectedNode = null;
        
        // 重置所有样式
        this.svg.selectAll('.node')
            .style('stroke-width', '2px')
            .style('stroke', '#fff')
            .style('opacity', 1);
        
        this.svg.selectAll('.link')
            .classed('highlighted', false)
            .style('opacity', 0.6);
    }

    dragstarted(event, d) {
        if (!event.active) this.simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }

    dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }

    dragended(event, d) {
        if (!event.active) this.simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }

    zoomIn() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 1.5);
    }

    zoomOut() {
        this.svg.transition().duration(300).call(this.zoom.scaleBy, 0.75);
    }

    resetView() {
        this.svg.transition().duration(500).call(this.zoom.transform, d3.zoomIdentity);
    }

    toggleFullscreen() {
        const container = document.getElementById('graph-canvas');
        if (!document.fullscreenElement) {
            container.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    showLoading() {
        document.getElementById('emptyState').style.display = 'none';
        document.getElementById('loadingState').style.display = 'block';
        document.getElementById('analyzeBtn').disabled = true;
    }

    hideLoading() {
        document.getElementById('loadingState').style.display = 'none';
        document.getElementById('analyzeBtn').disabled = false;
    }

    showGraphControls() {
        document.getElementById('graphControls').style.display = 'flex';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 全局实例，方便调试
let philosophyGraph = null;

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    philosophyGraph = new PhilosophyKnowledgeGraph();
    window.philosophyGraph = philosophyGraph; // 全局访问
});

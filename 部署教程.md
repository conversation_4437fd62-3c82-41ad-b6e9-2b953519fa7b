# 🚀 一键部署到网上

## 最简单的方法：Vercel部署（推荐）

### 1️⃣ 准备GitHub账号
- 访问 https://github.com 
- 注册或登录GitHub账号

### 2️⃣ 上传项目到GitHub
1. 登录GitHub后，点击右上角 "+" → "New repository"
2. 仓库名填写：`philosophy-knowledge-graph`
3. 选择 "Public"（公开）
4. 点击 "Create repository"
5. 按照页面提示上传项目文件

### 3️⃣ 一键部署到Vercel
1. 访问 https://vercel.com
2. 用GitHub账号登录
3. 点击 "Import Project"
4. 选择刚创建的 `philosophy-knowledge-graph` 仓库
5. 点击 "Deploy" 
6. 等待部署完成（约1-2分钟）

### 🎉 完成！
部署成功后会得到一个网址，比如：
`https://philosophy-knowledge-graph-xxx.vercel.app`

## 方法2：Netlify部署

### 步骤：
1. 访问 https://netlify.com
2. 用GitHub账号登录
3. 点击 "New site from Git"
4. 选择GitHub仓库
5. 点击 "Deploy site"

## 方法3：GitHub Pages（免费但功能有限）

### 步骤：
1. 在GitHub仓库中，点击 "Settings"
2. 滚动到 "Pages" 部分
3. Source选择 "Deploy from a branch"
4. Branch选择 "main"
5. 点击 "Save"

⚠️ **注意**：GitHub Pages不支持后端API，只能使用本地数据

## 🔧 部署后的配置

部署成功后，网站会自动检测环境：
- 线上环境：使用Vercel的serverless函数调用API
- 本地环境：使用本地代理服务器

## ✅ 验证部署

1. 打开部署的网址
2. 点击"测试API"按钮
3. 如果显示"API测试成功"，说明部署完成！

## 📱 分享你的网站

部署成功后，你可以：
- 把网址分享给朋友
- 在社交媒体上展示
- 用于教学演示

## 🛠️ 修改网站

要修改网站内容：
1. 在GitHub上直接编辑文件
2. 或者在本地修改后推送到GitHub
3. Vercel会自动重新部署

## 💰 费用说明

- **Vercel**：免费额度足够个人使用
- **Netlify**：免费额度充足
- **GitHub Pages**：完全免费

推荐使用Vercel，功能最完整！

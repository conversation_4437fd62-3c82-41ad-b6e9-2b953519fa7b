const https = require('https');

exports.handler = async (event, context) => {
    console.log('函数被调用，方法:', event.httpMethod);
    console.log('请求体:', event.body);

    // 设置CORS头
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Content-Type': 'application/json'
    };

    // 处理预检请求
    if (event.httpMethod === 'OPTIONS') {
        console.log('处理OPTIONS请求');
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    // 只允许POST请求
    if (event.httpMethod !== 'POST') {
        console.log('不允许的方法:', event.httpMethod);
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        // API配置
        const API_CONFIG = {
            hostname: 'openrouter.ai',
            path: '/api/v1/chat/completions',
            apiKey: '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',
            model: 'google/gemini-2.5-pro'
        };

        // 解析请求体
        let requestData;
        try {
            requestData = JSON.parse(event.body);
            console.log('解析的请求数据:', requestData);
        } catch (e) {
            console.error('JSON解析错误:', e);
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Invalid JSON in request body' })
            };
        }

        const { messages, temperature = 0.7, max_tokens = 4000 } = requestData;

        if (!messages || !Array.isArray(messages)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ error: 'Messages array is required' })
            };
        }

        // 构建API请求数据
        const apiRequestData = JSON.stringify({
            model: API_CONFIG.model,
            messages,
            temperature,
            max_tokens
        });

        console.log('发送到OpenRouter的数据:', apiRequestData);

        // 调用OpenRouter API
        const apiResponse = await new Promise((resolve, reject) => {
            const options = {
                hostname: API_CONFIG.hostname,
                path: API_CONFIG.path,
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${API_CONFIG.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://enchanting-scone-a52f15.netlify.app',
                    'X-Title': '哲学知识图谱学习网站',
                    'Content-Length': Buffer.byteLength(apiRequestData)
                }
            };

            const req = https.request(options, (res) => {
                let responseBody = '';
                
                res.on('data', (chunk) => {
                    responseBody += chunk;
                });

                res.on('end', () => {
                    console.log('OpenRouter响应状态:', res.statusCode);
                    console.log('OpenRouter响应头:', res.headers);
                    console.log('OpenRouter响应体长度:', responseBody.length);
                    
                    if (res.statusCode >= 200 && res.statusCode < 300) {
                        try {
                            const jsonResponse = JSON.parse(responseBody);
                            resolve({
                                statusCode: res.statusCode,
                                data: jsonResponse
                            });
                        } catch (e) {
                            console.error('解析OpenRouter响应失败:', e);
                            reject(new Error('Invalid JSON response from OpenRouter'));
                        }
                    } else {
                        console.error('OpenRouter API错误:', responseBody);
                        reject(new Error(`OpenRouter API error: ${res.statusCode} - ${responseBody}`));
                    }
                });
            });

            req.on('error', (error) => {
                console.error('请求OpenRouter时发生错误:', error);
                reject(error);
            });

            req.write(apiRequestData);
            req.end();
        });

        console.log('OpenRouter API调用成功');
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(apiResponse.data)
        };

    } catch (error) {
        console.error('函数执行错误:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: error.message || 'Internal server error',
                details: 'Failed to process AI request',
                timestamp: new Date().toISOString()
            })
        };
    }
};

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哲学知识图谱学习网站</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        #app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* 顶部标题 */
        .header {
            text-align: center;
            padding: 2rem 1rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .api-status {
            background: rgba(255,255,255,0.15);
            padding: 12px;
            border-radius: 12px;
            margin-top: 15px;
            backdrop-filter: blur(10px);
        }

        /* 搜索区域 */
        .search-container {
            padding: 0 2rem 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .search-box {
            display: flex;
            max-width: 700px;
            width: 100%;
            background: white;
            border-radius: 50px;
            padding: 0.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }

        #topicInput {
            flex: 1;
            border: none;
            outline: none;
            padding: 1rem 1.5rem;
            font-size: 16px;
            background: transparent;
        }

        #topicInput::placeholder {
            color: #999;
        }

        #analyzeBtn, #testApiBtn {
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        #analyzeBtn {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        #testApiBtn {
            background: #43e97b;
            margin-left: 10px;
        }

        #analyzeBtn:hover, #testApiBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        #analyzeBtn:disabled, #testApiBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .search-tips {
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 14px;
        }

        .topic-tag {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid rgba(255,255,255,0.3);
            margin: 4px;
            display: inline-block;
        }
        
        .topic-tag:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            gap: 1rem;
            padding: 0 1rem 1rem;
            max-height: calc(100vh - 250px);
        }

        /* 图谱容器 */
        .graph-container {
            flex: 1;
            position: relative;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        #graph-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            min-height: 500px;
        }

        #graph-svg {
            width: 100%;
            height: 100%;
            display: none;
        }

        /* 加载状态 */
        .loading-state {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 空状态 */
        .empty-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #666;
            max-width: 90%;
        }

        .empty-state .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .empty-state h3 {
            margin-bottom: 0.5rem;
            color: #333;
        }

        /* 图谱控制工具栏 */
        .graph-controls {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .graph-controls button {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255,255,255,0.9);
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .graph-controls button:hover {
            background: white;
            transform: scale(1.05);
        }

        /* 侧边栏 */
        .sidebar {
            width: 350px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar.active {
            display: flex;
        }

        .sidebar-header {
            padding: 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        #closeSidebar {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        #closeSidebar:hover {
            background: rgba(255,255,255,0.2);
        }

        .sidebar-content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
        }

        .concept-info > div {
            margin-bottom: 2rem;
        }

        .concept-info h4 {
            color: #667eea;
            margin-bottom: 0.8rem;
            font-size: 1.1rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.3rem;
        }

        .concept-info p {
            line-height: 1.7;
            color: #555;
        }

        .concept-info ul {
            list-style: none;
        }

        .concept-info li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .concept-info li:last-child {
            border-bottom: none;
        }

        #relatedConcepts {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .related-tag {
            background: #f0f4ff;
            color: #667eea;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .related-tag:hover {
            background: #667eea;
            color: white;
        }

        /* 图谱节点样式 */
        .node {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .node:hover {
            stroke-width: 3px;
        }

        .node-label {
            fill: #333;
            font-size: 12px;
            text-anchor: middle;
            pointer-events: none;
            font-weight: 500;
        }

        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 2px;
        }

        .link.highlighted {
            stroke: #667eea;
            stroke-opacity: 0.8;
            stroke-width: 3px;
        }

        /* 底部信息 */
        .footer {
            text-align: center;
            padding: 1rem;
            color: rgba(255,255,255,0.8);
            font-size: 0.9rem;
        }

        /* 消息提示 */
        .message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 10px;
            z-index: 2000;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
            text-align: center;
            animation: slideDown 0.3s ease;
            max-width: 90%;
        }

        .message.success {
            background: linear-gradient(45deg, #43e97b, #52c985);
            color: white;
        }

        .message.error {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            color: white;
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                max-height: none;
            }
            
            .sidebar {
                width: 100%;
                max-height: 50vh;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .search-box {
                flex-direction: column;
                border-radius: 15px;
                gap: 10px;
            }
            
            #analyzeBtn, #testApiBtn {
                border-radius: 10px;
                margin-left: 0 !important;
                width: 100%;
            }
            
            #graph-canvas {
                min-height: 400px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 顶部标题栏 -->
        <header class="header">
            <h1>🧠 哲学知识图谱</h1>
            <p>基于AI的哲学概念分析与可视化平台</p>
            <div class="api-status">
                <div style="color: rgba(255,255,255,0.95); font-size: 14px; margin-bottom: 8px;">
                    🤖 <strong>OpenRouter + Google Gemini 2.5 Pro</strong>
                </div>
                <div style="color: rgba(255,255,255,0.8); font-size: 13px;">
                    🔄 智能多重API调用策略 • 🛡️ 自动错误处理 • 📚 本地数据回退
                </div>
            </div>
        </header>

        <!-- 搜索区域 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" id="topicInput" placeholder="🔍 输入哲学主题：存在主义、道德哲学、认识论、美学、政治哲学..." />
                <button id="analyzeBtn">🚀 生成图谱</button>
                <button id="testApiBtn">🔧 测试API</button>
            </div>
            <div class="search-tips">
                <div style="margin-bottom: 10px;">💡 <strong>快速开始</strong>：点击下方标签或先测试API连接</div>
                <div>
                    <span class="topic-tag" onclick="setTopic('存在主义')">存在主义</span>
                    <span class="topic-tag" onclick="setTopic('道德哲学')">道德哲学</span>
                    <span class="topic-tag" onclick="setTopic('认识论')">认识论</span>
                    <span class="topic-tag" onclick="setTopic('美学')">美学</span>
                    <span class="topic-tag" onclick="setTopic('政治哲学')">政治哲学</span>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 图谱可视化区域 -->
            <div class="graph-container">
                <div id="graph-canvas">
                    <div class="loading-state" id="loadingState">
                        <div class="spinner"></div>
                        <p>🤖 AI正在深度分析概念关系...</p>
                        <div style="margin-top: 10px; font-size: 14px; opacity: 0.8;">
                            请稍候，这可能需要几秒钟
                        </div>
                    </div>
                    <div class="empty-state" id="emptyState">
                        <div class="icon">🧠</div>
                        <h3>开始探索哲学世界</h3>
                        <p>点击上方主题标签快速开始，或输入自定义哲学主题</p>
                        <div style="margin-top: 25px; color: #666; font-size: 14px; line-height: 1.6;">
                            <div><strong>🔧 技术特色：</strong></div>
                            <div>• 5种API调用方法自动切换</div>
                            <div>• 实时错误处理和状态反馈</div>
                            <div>• 高质量本地数据库备用</div>
                            <div>• 交互式图谱可视化</div>
                        </div>
                    </div>
                    <svg id="graph-svg"></svg>
                </div>
                
                <!-- 图谱控制工具栏 -->
                <div class="graph-controls" id="graphControls" style="display: none;">
                    <button id="zoomIn" title="放大图谱">+</button>
                    <button id="zoomOut" title="缩小图谱">-</button>
                    <button id="resetView" title="重置视图">⌂</button>
                    <button id="fullscreen" title="全屏模式">⛶</button>
                </div>
            </div>

            <!-- 侧边栏详情 -->
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3 id="conceptTitle">📖 概念详情</h3>
                    <button id="closeSidebar">×</button>
                </div>
                <div class="sidebar-content">
                    <div class="concept-info" id="conceptInfo">
                        <div class="concept-description">
                            <h4>💡 概念定义</h4>
                            <p id="conceptDescription">选择图谱中的节点查看详细信息</p>
                        </div>
                        
                        <div class="key-thinkers">
                            <h4>👨‍🏫 代表人物</h4>
                            <ul id="thinkersList"></ul>
                        </div>
                        
                        <div class="important-works">
                            <h4>📚 重要著作</h4>
                            <ul id="worksList"></ul>
                        </div>
                        
                        <div class="related-concepts">
                            <h4>🔗 相关概念</h4>
                            <div id="relatedConcepts"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <footer class="footer">
            <p>🚀 基于多重API调用策略的哲学概念分析与可视化平台</p>
            <div style="margin-top: 5px; font-size: 12px; opacity: 0.8;">
                Powered by OpenRouter API + Google Gemini 2.5 Pro + D3.js
            </div>
        </footer>
    </div>

    <!-- 引入D3.js -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    
    <script>
        // 哲学知识图谱主类
        class PhilosophyKnowledgeGraph {
            constructor() {
                this.svg = null;
                this.simulation = null;
                this.nodes = [];
                this.links = [];
                this.width = 0;
                this.height = 0;
                this.zoom = null;
                this.selectedNode = null;
                this.apiWorking = false;
                
                // API配置
                this.apiConfig = {
                    baseUrl: 'https://openrouter.ai/api/v1/chat/completions',
                    apiKey: '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',
                    model: 'google/gemini-2.5-pro',
                    corsProxies: [
                        'https://api.allorigins.win/raw?url=',
                        'https://cors-proxy.htmldriven.com/?url=',
                        'https://thingproxy.freeboard.io/fetch/'
                    ]
                };
                
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.setupGraph();
                this.showWelcomeMessage();
            }

            showWelcomeMessage() {
                setTimeout(() => {
                    this.showMessage('🎉 哲学知识图谱已就绪！', '点击"测试API"检查连接状态，或直接选择主题开始探索', 'success');
                }, 1000);
            }

            setupEventListeners() {
                document.getElementById('analyzeBtn').addEventListener('click', () => {
                    this.analyzePhilosophyTopic();
                });

                document.getElementById('testApiBtn').addEventListener('click', () => {
                    this.testAllMethods();
                });

                document.getElementById('topicInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.analyzePhilosophyTopic();
                    }
                });

                document.getElementById('closeSidebar').addEventListener('click', () => {
                    this.closeSidebar();
                });

                document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
                document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
                document.getElementById('resetView').addEventListener('click', () => this.resetView());
                document.getElementById('fullscreen').addEventListener('click', () => this.toggleFullscreen());

                window.addEventListener('resize', () => {
                    this.updateGraphSize();
                });
            }

            setupGraph() {
                const container = document.getElementById('graph-canvas');
                const rect = container.getBoundingClientRect();
                this.width = rect.width;
                this.height = rect.height;

                this.svg = d3.select('#graph-svg')
                    .attr('width', this.width)
                    .attr('height', this.height);

                this.zoom = d3.zoom()
                    .scaleExtent([0.3, 3])
                    .on('zoom', (event) => {
                        this.svg.select('g').attr('transform', event.transform);
                    });

                this.svg.call(this.zoom);
                this.svg.append('g');
            }

            updateGraphSize() {
                const container = document.getElementById('graph-canvas');
                const rect = container.getBoundingClientRect();
                this.width = rect.width;
                this.height = rect.height;

                this.svg
                    .attr('width', this.width)
                    .attr('height', this.height);

                if (this.simulation) {
                    this.simulation
                        .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                        .alpha(0.3)
                        .restart();
                }
            }

            async analyzePhilosophyTopic() {
                const topic = document.getElementById('topicInput').value.trim();
                if (!topic) {
                    this.showMessage('⚠️ 请输入哲学主题', '在搜索框中输入您感兴趣的哲学概念', 'error');
                    return;
                }

                this.showLoading();
                
                try {
                    console.log('🧠 开始AI分析主题:', topic);
                    const conceptData = await this.analyzeTopicWithAI(topic);
                    
                    console.log('✅ AI分析成功，渲染图谱');
                    this.renderGraph(conceptData);
                    
                    this.hideLoading();
                    this.showGraphControls();
                    this.showMessage('🎉 AI分析完成！', `成功生成 ${conceptData.nodes.length} 个概念节点`, 'success');
                    
                } catch (error) {
                    console.error('❌ AI分析失败:', error);
                    this.hideLoading();
                    
                    const useLocalData = confirm(
                        `🤖 AI分析遇到问题：${error.message}\n\n` +
                        `💡 我们为您准备了高质量的本地哲学数据库作为备选方案。\n\n` +
                        `点击"确定"使用本地数据，"取消"重试API调用。`
                    );
                    
                    if (useLocalData) {
                        console.log('📚 使用本地哲学数据库');
                        const fallbackData = this.generateConceptData(topic);
                        this.renderGraph(fallbackData);
                        this.hideLoading();
                        this.showGraphControls();
                        this.showMessage('📚 本地数据已加载', '使用精心整理的哲学概念数据', 'success');
                    }
                }
            }

            async analyzeTopicWithAI(topic) {
                console.log(`🔍 开始AI分析: ${topic}`);
                
                const prompt = `请分析哲学主题"${topic}"，生成知识图谱数据。

要求：
1. 识别6-10个核心概念、相关理论、重要思想家
2. 分析概念间的逻辑关系和重要程度
3. 严格按照JSON格式返回，不要其他文字：

{
  "nodes": [
    {
      "id": "unique-id",
      "name": "概念名称",
      "type": "main|core|concept|related|person",
      "importance": 100,
      "description": "详细描述",
      "thinkers": ["思想家1", "思想家2"],
      "works": ["著作1", "著作2"]
    }
  ],
  "links": [
    {
      "source": "source-id",
      "target": "target-id",
      "strength": 0.9
    }
  ]
}

类型说明：
- main: 主题 (importance: 100)
- core: 核心概念 (70-90)
- concept: 一般概念 (50-70)
- related: 相关理论 (40-60)
- person: 思想家 (60-90)

连接强度：0.3-1.0`;

                // 尝试多种API调用方法
                const methods = [
                    () => this.directAPICall(prompt),
                    () => this.proxyAPICall(prompt, 0),
                    () => this.proxyAPICall(prompt, 1),
                    () => this.proxyAPICall(prompt, 2)
                ];

                let lastError = null;
                
                for (let i = 0; i < methods.length; i++) {
                    try {
                        console.log(`🔄 尝试方法 ${i + 1}/${methods.length}`);
                        const result = await methods[i]();
                        console.log(`✅ 方法 ${i + 1} 成功`);
                        this.apiWorking = true;
                        return result;
                    } catch (error) {
                        console.log(`❌ 方法 ${i + 1} 失败:`, error.message);
                        lastError = error;
                        if (i < methods.length - 1) {
                            await this.delay(500);
                        }
                    }
                }
                
                throw new Error(`所有API调用方法都失败了。最后错误: ${lastError?.message || '未知错误'}`);
            }

            async directAPICall(prompt) {
                console.log('📡 尝试直接API调用...');
                
                const response = await fetch(this.apiConfig.baseUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': window.location.origin,
                        'X-Title': '哲学知识图谱'
                    },
                    body: JSON.stringify({
                        model: this.apiConfig.model,
                        messages: [{ role: 'user', content: prompt }],
                        temperature: 0.7,
                        max_tokens: 4000
                    })
                });

                return await this.processAPIResponse(response);
            }

            async proxyAPICall(prompt, proxyIndex) {
                const proxy = this.apiConfig.corsProxies[proxyIndex];
                console.log(`🔄 尝试CORS代理 ${proxyIndex + 1}: ${proxy}`);
                
                const targetUrl = encodeURIComponent(this.apiConfig.baseUrl);
                const proxyUrl = `${proxy}${targetUrl}`;
                
                const response = await fetch(proxyUrl, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.apiConfig.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        model: this.apiConfig.model,
                        messages: [{ role: 'user', content: prompt }],
                        temperature: 0.7,
                        max_tokens: 4000
                    })
                });

                return await this.processAPIResponse(response);
            }

            async processAPIResponse(response) {
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                
                if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                    throw new Error('API响应格式错误：缺少必要字段');
                }

                const content = data.choices[0].message.content;
                return this.parseAIContent(content);
            }

            parseAIContent(content) {
                console.log('🔍 解析AI响应内容...');
                
                try {
                    let cleanContent = content.trim();
                    
                    // 移除markdown标记
                    cleanContent = cleanContent.replace(/```json\s*\n?/g, '').replace(/\n?```\s*$/g, '');
                    cleanContent = cleanContent.replace(/```\s*\n?/g, '').replace(/\n?```\s*$/g, '');
                    
                    // 查找JSON部分
                    const jsonStart = cleanContent.indexOf('{');
                    const jsonEnd = cleanContent.lastIndexOf('}') + 1;
                    
                    if (jsonStart >= 0 && jsonEnd > jsonStart) {
                        cleanContent = cleanContent.substring(jsonStart, jsonEnd);
                    }
                    
                    const conceptData = JSON.parse(cleanContent);
                    
                    // 验证数据结构
                    if (!conceptData.nodes || !Array.isArray(conceptData.nodes) || conceptData.nodes.length === 0) {
                        throw new Error('无效的节点数据');
                    }
                    
                    if (!conceptData.links || !Array.isArray(conceptData.links)) {
                        conceptData.links = [];
                    }

                    // 补全节点信息
                    conceptData.nodes.forEach(node => {
                        if (!node.id) node.id = node.name.replace(/[^\w\u4e00-\u9fff]/g, '-').toLowerCase();
                        if (!node.description) node.description = `${node.name}是重要的哲学概念。`;
                        if (!node.thinkers) node.thinkers = ['相关哲学家'];
                        if (!node.works) node.works = ['相关著作'];
                        if (!node.type) node.type = 'concept';
                        if (!node.importance) node.importance = Math.floor(Math.random() * 30) + 50;
                    });

                    console.log('✅ 成功解析概念数据:', conceptData);
                    return conceptData;
                    
                } catch (parseError) {
                    console.error('❌ JSON解析失败:', parseError);
                    throw new Error(`无法解析AI返回的数据: ${parseError.message}`);
                }
            }

            async testAllMethods() {
                console.log('🧪 开始测试所有API方法...');
                
                const testPrompt = '请简单回复"测试成功"';
                const results = [];
                
                this.showMessage('🔧 正在测试API连接...', '请稍候，正在测试各种连接方法', 'success');
                
                const methods = [
                    { name: '直接API调用', fn: () => this.directAPICall(testPrompt) },
                    { name: 'CORS代理1', fn: () => this.proxyAPICall(testPrompt, 0) },
                    { name: 'CORS代理2', fn: () => this.proxyAPICall(testPrompt, 1) },
                    { name: 'CORS代理3', fn: () => this.proxyAPICall(testPrompt, 2) }
                ];

                for (let i = 0; i < methods.length; i++) {
                    const method = methods[i];
                    
                    try {
                        await method.fn();
                        results.push({ name: method.name, status: '✅ 成功' });
                        console.log(`✅ ${method.name}: 成功`);
                    } catch (error) {
                        results.push({ name: method.name, status: '❌ 失败', error: error.message });
                        console.log(`❌ ${method.name}: ${error.message}`);
                    }
                    
                    await this.delay(1000);
                }
                
                this.showTestResults(results);
            }

            showTestResults(results) {
                const successfulMethods = results.filter(r => r.status.includes('成功'));
                const failedMethods = results.filter(r => r.status.includes('失败'));
                
                let message = '🔧 API连接测试完成\n\n';
                
                if (successfulMethods.length > 0) {
                    message += `✅ 可用方法 (${successfulMethods.length}个):\n`;
                    successfulMethods.forEach(m => {
                        message += `• ${m.name}\n`;
                    });
                    message += '\n';
                }
                
                if (failedMethods.length > 0) {
                    message += `❌ 不可用方法 (${failedMethods.length}个):\n`;
                    failedMethods.forEach(m => {
                        message += `• ${m.name}\n`;
                    });
                }
                
                if (successfulMethods.length > 0) {
                    message += '\n🎉 恭喜！至少有一种方法可以连接API。\n现在可以正常使用AI分析功能了！';
                    this.apiWorking = true;
                    this.showMessage('🎉 API测试成功！', `${successfulMethods.length}种方法可用，现在可以使用AI功能`, 'success');
                } else {
                    message += '\n⚠️ 所有API连接方法都失败了。\n建议使用本地数据模式继续体验。';
                    this.apiWorking = false;
                    this.showMessage('⚠️ API连接失败', '所有方法都无法连接，建议使用本地数据', 'error');
                }
                
                alert(message);
            }

            generateConceptData(topic) {
                // 丰富的本地哲学数据库
                const conceptMaps = {
                    '存在主义': {
                        nodes: [
                            { id: 'existentialism', name: '存在主义', type: 'main', importance: 100,
                              description: '20世纪哲学流派，强调个体存在的独特性和主观体验，认为存在先于本质。',
                              thinkers: ['萨特', '加缪', '克尔凯郭尔', '海德格尔'],
                              works: ['《存在与虚无》', '《局外人》', '《恐惧与颤栗》', '《存在与时间》'] },
                            { id: 'authentic-existence', name: '真实存在', type: 'core', importance: 85,
                              description: '个体摆脱社会角色束缚，以真诚方式面对自己存在状态的生活方式。',
                              thinkers: ['海德格尔', '萨特'],
                              works: ['《存在与时间》', '《存在与虚无》'] },
                            { id: 'bad-faith', name: '恶劣信念', type: 'core', importance: 80,
                              description: '萨特提出的概念，指个体逃避自由和责任，假装自己没有选择的自欺行为。',
                              thinkers: ['萨特'],
                              works: ['《存在与虚无》'] },
                            { id: 'freedom', name: '自由', type: 'core', importance: 90,
                              description: '存在主义核心概念，人被判定是自由的，必须承担选择的全部责任。',
                              thinkers: ['萨特', '加缪'],
                              works: ['《存在与虚无》', '《西西弗的神话》'] },
                            { id: 'anxiety', name: '焦虑', type: 'concept', importance: 70,
                              description: '面对存在的无意义和自由选择时产生的根本性情绪体验。',
                              thinkers: ['克尔凯郭尔', '海德格尔'],
                              works: ['《恐惧与颤栗》', '《存在与时间》'] },
                            { id: 'absurd', name: '荒诞', type: 'related', importance: 75,
                              description: '加缪哲学的核心，指人类寻求意义与世界无意义之间的冲突。',
                              thinkers: ['加缪'],
                              works: ['《西西弗的神话》', '《局外人》'] },
                            { id: 'sartre', name: '萨特', type: 'person', importance: 95,
                              description: '法国哲学家，存在主义主要代表人物。',
                              thinkers: ['萨特'],
                              works: ['《存在与虚无》', '《恶心》'] }
                        ],
                        links: [
                            { source: 'existentialism', target: 'authentic-existence', strength: 0.9 },
                            { source: 'existentialism', target: 'bad-faith', strength: 0.8 },
                            { source: 'existentialism', target: 'freedom', strength: 0.95 },
                            { source: 'authentic-existence', target: 'bad-faith', strength: 0.7 },
                            { source: 'freedom', target: 'anxiety', strength: 0.8 },
                            { source: 'sartre', target: 'existentialism', strength: 0.9 },
                            { source: 'sartre', target: 'bad-faith', strength: 0.8 },
                            { source: 'absurd', target: 'existentialism', strength: 0.6 }
                        ]
                    },
                    '道德哲学': {
                        nodes: [
                            { id: 'ethics', name: '道德哲学', type: 'main', importance: 100,
                              description: '研究道德原则、价值判断和伦理行为的哲学分支。',
                              thinkers: ['亚里士多德', '康德', '密尔'],
                              works: ['《尼各马可伦理学》', '《道德形而上学基础》', '《功利主义》'] },
                            { id: 'virtue-ethics', name: '美德伦理学', type: 'core', importance: 85,
                              description: '强调品格和美德，认为道德行为源于良好的品格特质。',
                              thinkers: ['亚里士多德', '阿奎那'],
                              works: ['《尼各马可伦理学》'] },
                            { id: 'deontology', name: '义务论', type: 'core', importance: 85,
                              description: '康德提出的道德理论，强调行为的动机和义务，而非后果。',
                              thinkers: ['康德'],
                              works: ['《道德形而上学基础》', '《实践理性批判》'] },
                            { id: 'consequentialism', name: '后果主义', type: 'core', importance: 80,
                              description: '判断行为道德性的标准是其产生的后果。',
                              thinkers: ['边沁', '密尔'],
                              works: ['《道德与立法原理导论》', '《功利主义》'] },
                            { id: 'categorical-imperative', name: '绝对命令', type: 'concept', importance: 75,
                              description: '康德道德哲学的核心，无条件的道德法则。',
                              thinkers: ['康德'],
                              works: ['《道德形而上学基础》'] }
                        ],
                        links: [
                            { source: 'ethics', target: 'virtue-ethics', strength: 0.9 },
                            { source: 'ethics', target: 'deontology', strength: 0.9 },
                            { source: 'ethics', target: 'consequentialism', strength: 0.85 },
                            { source: 'deontology', target: 'categorical-imperative', strength: 0.95 }
                        ]
                    }
                };

                return conceptMaps[topic] || this.generateGenericConceptData(topic);
            }

            generateGenericConceptData(topic) {
                const genericNodes = [
                    { id: 'main', name: topic, type: 'main', importance: 100,
                      description: `${topic}是哲学的重要研究领域，涉及基本的概念和理论问题。`,
                      thinkers: ['相关哲学家'],
                      works: ['相关哲学著作'] },
                    { id: 'concept1', name: '核心概念A', type: 'core', importance: 80,
                      description: `${topic}领域的核心概念之一。`,
                      thinkers: ['重要思想家A'],
                      works: ['重要著作A'] },
                    { id: 'concept2', name: '核心概念B', type: 'core', importance: 75,
                      description: `${topic}领域的另一个重要概念。`,
                      thinkers: ['重要思想家B'],
                      works: ['重要著作B'] },
                    { id: 'theory1', name: '相关理论', type: 'related', importance: 65,
                      description: `与${topic}相关的重要理论框架。`,
                      thinkers: ['理论家'],
                      works: ['理论著作'] }
                ];

                return {
                    nodes: genericNodes,
                    links: [
                        { source: 'main', target: 'concept1', strength: 0.9 },
                        { source: 'main', target: 'concept2', strength: 0.8 },
                        { source: 'concept1', target: 'theory1', strength: 0.7 }
                    ]
                };
            }

            renderGraph(data) {
                this.nodes = data.nodes;
                this.links = data.links;

                this.svg.select('g').selectAll('*').remove();

                this.simulation = d3.forceSimulation(this.nodes)
                    .force('link', d3.forceLink(this.links).id(d => d.id).distance(d => 120 - d.strength * 60))
                    .force('charge', d3.forceManyBody().strength(-400))
                    .force('center', d3.forceCenter(this.width / 2, this.height / 2))
                    .force('collision', d3.forceCollide().radius(d => this.getNodeRadius(d) + 10));

                const g = this.svg.select('g');

                const link = g.append('g')
                    .selectAll('line')
                    .data(this.links)
                    .enter().append('line')
                    .attr('class', 'link')
                    .style('stroke-width', d => d.strength * 4);

                const node = g.append('g')
                    .selectAll('.node-group')
                    .data(this.nodes)
                    .enter().append('g')
                    .attr('class', 'node-group')
                    .style('cursor', 'pointer')
                    .call(d3.drag()
                        .on('start', (event, d) => this.dragstarted(event, d))
                        .on('drag', (event, d) => this.dragged(event, d))
                        .on('end', (event, d) => this.dragended(event, d)));

                node.append('circle')
                    .attr('class', 'node')
                    .attr('r', d => this.getNodeRadius(d))
                    .style('fill', d => this.getNodeColor(d))
                    .style('stroke', '#fff')
                    .style('stroke-width', 2)
                    .on('click', (event, d) => this.nodeClicked(event, d))
                    .on('mouseover', (event, d) => this.nodeMouseOver(event, d))
                    .on('mouseout', (event, d) => this.nodeMouseOut(event, d));

                node.append('text')
                    .attr('class', 'node-label')
                    .attr('dy', d => this.getNodeRadius(d) + 15)
                    .style('font-size', d => d.type === 'main' ? '14px' : '12px')
                    .style('font-weight', d => d.type === 'main' ? 'bold' : 'normal')
                    .text(d => d.name);

                this.simulation.on('tick', () => {
                    link
                        .attr('x1', d => d.source.x)
                        .attr('y1', d => d.source.y)
                        .attr('x2', d => d.target.x)
                        .attr('y2', d => d.target.y);

                    node
                        .attr('transform', d => `translate(${d.x},${d.y})`);
                });

                document.getElementById('graph-svg').style.display = 'block';
            }

            getNodeRadius(node) {
                const baseRadius = 12;
                const maxRadius = 30;
                return baseRadius + (node.importance / 100) * (maxRadius - baseRadius);
            }

            getNodeColor(node) {
                const colors = {
                    'main': '#667eea',
                    'core': '#764ba2',
                    'concept': '#f093fb',
                    'related': '#4facfe',
                    'person': '#43e97b'
                };
                return colors[node.type] || '#999';
            }

            nodeClicked(event, node) {
                this.selectedNode = node;
                this.showNodeDetails(node);
                this.highlightConnections(node);
            }

            nodeMouseOver(event, node) {
                d3.select(event.target)
                    .style('stroke-width', '4px')
                    .style('stroke', '#333')
                    .transition()
                    .duration(200)
                    .attr('r', this.getNodeRadius(node) + 3);
            }

            nodeMouseOut(event, node) {
                if (this.selectedNode !== node) {
                    d3.select(event.target)
                        .style('stroke-width', '2px')
                        .style('stroke', '#fff')
                        .transition()
                        .duration(200)
                        .attr('r', this.getNodeRadius(node));
                }
            }

            highlightConnections(node) {
                this.svg.selectAll('.link')
                    .classed('highlighted', false)
                    .style('opacity', 0.3);

                this.svg.selectAll('.link')
                    .filter(d => d.source.id === node.id || d.target.id === node.id)
                    .classed('highlighted', true)
                    .style('opacity', 1);

                this.svg.selectAll('.node')
                    .style('opacity', 0.3);

                this.svg.selectAll('.node')
                    .filter(d => {
                        if (d.id === node.id) return true;
                        return this.links.some(link => 
                            (link.source.id === node.id && link.target.id === d.id) ||
                            (link.target.id === node.id && link.source.id === d.id)
                        );
                    })
                    .style('opacity', 1);
            }

            showNodeDetails(node) {
                const sidebar = document.getElementById('sidebar');
                const conceptTitle = document.getElementById('conceptTitle');
                const conceptDescription = document.getElementById('conceptDescription');
                const thinkersList = document.getElementById('thinkersList');
                const worksList = document.getElementById('worksList');
                const relatedConcepts = document.getElementById('relatedConcepts');

                conceptTitle.textContent = node.name;
                conceptDescription.textContent = node.description || `${node.name}是重要的哲学概念。`;

                thinkersList.innerHTML = '';
                (node.thinkers || ['相关哲学家']).forEach(thinker => {
                    const li = document.createElement('li');
                    li.textContent = thinker;
                    thinkersList.appendChild(li);
                });

                worksList.innerHTML = '';
                (node.works || ['相关著作']).forEach(work => {
                    const li = document.createElement('li');
                    li.textContent = work;
                    worksList.appendChild(li);
                });

                relatedConcepts.innerHTML = '';
                const relatedNodes = this.getRelatedNodes(node);
                relatedNodes.forEach(relatedNode => {
                    const tag = document.createElement('div');
                    tag.className = 'related-tag';
                    tag.textContent = relatedNode.name;
                    tag.addEventListener('click', () => {
                        this.nodeClicked(null, relatedNode);
                    });
                    relatedConcepts.appendChild(tag);
                });

                sidebar.classList.add('active');
            }

            getRelatedNodes(node) {
                return this.links
                    .filter(link => link.source.id === node.id || link.target.id === node.id)
                    .map(link => link.source.id === node.id ? link.target : link.source)
                    .slice(0, 6);
            }

            closeSidebar() {
                document.getElementById('sidebar').classList.remove('active');
                this.selectedNode = null;
                
                this.svg.selectAll('.node')
                    .style('stroke-width', '2px')
                    .style('stroke', '#fff')
                    .style('opacity', 1);
                
                this.svg.selectAll('.link')
                    .classed('highlighted', false)
                    .style('opacity', 0.6);
            }

            dragstarted(event, d) {
                if (!event.active) this.simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }

            dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }

            dragended(event, d) {
                if (!event.active) this.simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }

            zoomIn() {
                this.svg.transition().duration(300).call(this.zoom.scaleBy, 1.5);
            }

            zoomOut() {
                this.svg.transition().duration(300).call(this.zoom.scaleBy, 0.75);
            }

            resetView() {
                this.svg.transition().duration(500).call(this.zoom.transform, d3.zoomIdentity);
            }

            toggleFullscreen() {
                const container = document.getElementById('graph-canvas');
                if (!document.fullscreenElement) {
                    container.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }

            showLoading() {
                document.getElementById('emptyState').style.display = 'none';
                document.getElementById('loadingState').style.display = 'block';
                document.getElementById('analyzeBtn').disabled = true;
            }

            hideLoading() {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('analyzeBtn').disabled = false;
            }

            showGraphControls() {
                document.getElementById('graphControls').style.display = 'flex';
            }

            showMessage(title, description, type = 'success') {
                // 移除之前的消息
                const existingMessage = document.querySelector('.message');
                if (existingMessage) {
                    existingMessage.remove();
                }

                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                    <div style="font-size: 14px; opacity: 0.9;">${description}</div>
                `;
                
                document.body.appendChild(messageDiv);
                
                setTimeout(() => {
                    messageDiv.style.opacity = '0';
                    setTimeout(() => messageDiv.remove(), 300);
                }, 4000);
            }

            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 全局函数
        function setTopic(topic) {
            document.getElementById('topicInput').value = topic;
            document.getElementById('analyzeBtn').click();
        }

        // 初始化
        let philosophyGraph = null;
        document.addEventListener('DOMContentLoaded', () => {
            philosophyGraph = new PhilosophyKnowledgeGraph();
            window.philosophyGraph = philosophyGraph;
        });
    </script>
</body>
</html>
